# DevRev to Zendesk Reverse Sync Workflow

## Overview
This workflow handles reverse synchronization of contacts from DevRev to Zendesk. When a ticket is created in DevRev, the system attempts to create the corresponding contact in Zendesk and establish a sync mapping between the two systems.

## Workflow Steps

1. **Ticket Creation in DevRev**: When a ticket is created, we extract the contact information
2. **Zendesk Contact Creation**: Attempt to create the contact in Zendesk
3. **Response Handling**:
   - **Success**: Create sync mapper record with Zendesk user ID as external_id and DevRev user ID as target
   - **Duplicate**: Either skip or update the existing contact (configurable)
   - **Error**: Handle appropriately based on error type

## Key Components

- `reverse_sync_workflow.py`: Main workflow implementation
- `zendesk_client.py`: Zendesk API client for user operations
- `sync_mapper_client.py`: DevRev sync mapper API client
- `config.yaml`: Configuration settings
- `requirements.txt`: Python dependencies

## Configuration

The workflow requires the following configuration:
- Zendesk API credentials
- DevRev API credentials
- Sync unit ID for the mapper records
- Handling strategy for duplicate contacts

## API Endpoints Used

- Zendesk: `POST /api/v2/users.json` for user creation
- DevRev: `POST /internal/airdrop.sync-mapper-record.create` for sync mapping 