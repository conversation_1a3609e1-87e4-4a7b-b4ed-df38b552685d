#!/usr/bin/env python3
"""
Analyze Production SLAs

This script analyzes the production SLAs to understand the current structure
"""

import json
from typing import Dict, List, Any

def analyze_slas():
    """Analyze the production SLAs"""
    try:
        with open('production_slas.json', 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("❌ production_slas.json not found")
        return
    
    slas = data.get('slas', [])
    
    print("🏭 PRODUCTION SLA ANALYSIS")
    print("=" * 60)
    print(f"Total Published SLAs: {len(slas)}")
    print()
    
    # Categorize SLAs
    default_slas = []
    child_slas = []
    insurer_slas = []
    other_slas = []
    
    total_policies = 0
    
    for sla in slas:
        name = sla['name']
        policy_count = len(sla.get('policies', []))
        total_policies += policy_count
        
        if name.lower() == 'default':
            default_slas.append((name, policy_count, sla['id']))
        elif name.startswith('Child ||'):
            child_slas.append((name, policy_count, sla['id']))
        elif name.startswith('Insurer ||'):
            insurer_slas.append((name, policy_count, sla['id']))
        else:
            other_slas.append((name, policy_count, sla['id']))
    
    print("📊 SLA BREAKDOWN:")
    print(f"├── Default SLAs: {len(default_slas)}")
    for name, policies, sla_id in default_slas:
        print(f"│   └── {name} ({policies} policies)")
    
    print(f"├── Child SLAs: {len(child_slas)}")
    for name, policies, sla_id in child_slas[:5]:  # Show first 5
        print(f"│   └── {name} ({policies} policies)")
    if len(child_slas) > 5:
        print(f"│   └── ... and {len(child_slas) - 5} more")
    
    print(f"├── Insurer SLAs: {len(insurer_slas)}")
    for name, policies, sla_id in insurer_slas[:5]:  # Show first 5
        print(f"│   └── {name} ({policies} policies)")
    if len(insurer_slas) > 5:
        print(f"│   └── ... and {len(insurer_slas) - 5} more")
    
    if other_slas:
        print(f"└── Other SLAs: {len(other_slas)}")
        for name, policies, sla_id in other_slas:
            print(f"    └── {name} ({policies} policies)")
    
    print()
    print("📈 SUMMARY:")
    print(f"Total SLAs: {len(slas)}")
    print(f"Total Policies: {total_policies}")
    print(f"Average Policies per SLA: {total_policies / len(slas):.1f}")
    
    # Check the Default SLA
    if default_slas:
        default_sla = next((sla for sla in slas if sla['name'].lower() == 'default'), None)
        if default_sla:
            print()
            print("🎯 DEFAULT SLA DETAILS:")
            print(f"Name: {default_sla['name']}")
            print(f"ID: {default_sla['id']}")
            print(f"Policies: {len(default_sla.get('policies', []))}")
            print(f"Status: {default_sla['status']}")
            
            if default_sla.get('policies'):
                print("Policy types:")
                for i, policy in enumerate(default_sla['policies'][:3]):  # Show first 3
                    print(f"  {i+1}. {policy['name']}")
                if len(default_sla['policies']) > 3:
                    print(f"  ... and {len(default_sla['policies']) - 3} more")
    
    print()
    print("💡 CONSOLIDATION OPTIONS:")
    print("1. Move all Child SLAs into Default SLA")
    print("2. Move all Insurer SLAs into Default SLA") 
    print("3. Move ALL SLAs into Default SLA")
    print("4. Create a new consolidated SLA")
    
    # Calculate consolidation impact
    child_policies = sum(policies for _, policies, _ in child_slas)
    insurer_policies = sum(policies for _, policies, _ in insurer_slas)
    other_policies = sum(policies for _, policies, _ in other_slas)
    default_policies = sum(policies for _, policies, _ in default_slas)
    
    print()
    print("📊 CONSOLIDATION IMPACT:")
    print(f"Current Default SLA: {default_policies} policies")
    print(f"Child SLAs: {child_policies} policies")
    print(f"Insurer SLAs: {insurer_policies} policies")
    print(f"Other SLAs: {other_policies} policies")
    print()
    print("If consolidated into Default SLA:")
    print(f"Total policies: {default_policies + child_policies + insurer_policies + other_policies}")

if __name__ == "__main__":
    analyze_slas()
