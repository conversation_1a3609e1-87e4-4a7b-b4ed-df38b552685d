#!/usr/bin/env python3
"""
Check current SLA status after consolidation
"""

import json
import requests

# You'll need to update this token from your browser
AUTH_TOKEN = 'YOUR_CURRENT_TOKEN_HERE'

HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US',
    'authorization': AUTH_TOKEN,
    'x-devrev-client-platform': 'web-product',
    'x-devrev-client-version': '7a3380a',
    'x-devrev-dev-user-don': 'don:identity:dvrv-in-1:devo/2113v1Epxh:devu/23'
}

def check_slas():
    """Check current SLA status"""
    try:
        response = requests.get(
            'https://app.devrev.ai/api/gateway/internal/slas.list?status=published',
            headers=HEADERS,
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            slas = data.get('slas', [])
            
            print("Current Published SLAs:")
            print("=" * 50)
            
            for sla in slas:
                print(f"📋 {sla['name']}")
                print(f"   ID: {sla['id']}")
                print(f"   Status: {sla['status']}")
                print(f"   Policies: {len(sla.get('policies', []))}")
                print()
            
            # Check for draft SLAs too
            response_draft = requests.get(
                'https://app.devrev.ai/api/gateway/internal/slas.list?status=draft',
                headers=HEADERS,
                timeout=30
            )
            
            if response_draft.status_code == 200:
                draft_data = response_draft.json()
                draft_slas = draft_data.get('slas', [])
                
                if draft_slas:
                    print("Current Draft SLAs:")
                    print("=" * 50)
                    
                    for sla in draft_slas:
                        print(f"📝 {sla['name']}")
                        print(f"   ID: {sla['id']}")
                        print(f"   Status: {sla['status']}")
                        print(f"   Policies: {len(sla.get('policies', []))}")
                        print()
        
        else:
            print(f"Failed to get SLAs: {response.status_code}")
            print("Please update the AUTH_TOKEN in this script with a fresh token from your browser")
            
    except Exception as e:
        print(f"Error: {e}")
        print("Please update the AUTH_TOKEN in this script with a fresh token from your browser")

if __name__ == "__main__":
    check_slas()
