#!/usr/bin/env python3
"""
Complete SLA Consolidation

This script will:
1. Publish the "Default SLA (Consolidated)" 
2. Archive all the old individual SLAs
3. Make the consolidated SLA the new default
"""

import json
import requests
import sys

# You'll need to get a fresh token from your browser
print("🔑 To complete the consolidation, you need to:")
print("1. Open DevRev in your browser")
print("2. Open Developer Tools (F12)")
print("3. Go to Network tab")
print("4. Refresh the SLA page")
print("5. Find any API request and copy the 'authorization' header")
print("6. Paste it below when prompted")
print()

AUTH_TOKEN = input("Enter your current authorization token: ").strip()

if not AUTH_TOKEN:
    print("❌ No token provided. Exiting.")
    sys.exit(1)

API_BASE_URL = "https://app.devrev.ai/api/gateway/internal"

HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US',
    'authorization': AUTH_TOKEN,
    'content-type': 'application/json',
    'x-devrev-client-platform': 'web-product',
    'x-devrev-client-version': '7a3380a',
    'x-devrev-dev-user-don': 'don:identity:dvrv-in-1:devo/2113v1Epxh:devu/23'
}

# Known SLA IDs from your screenshots
CONSOLIDATED_SLA_ID = "don:core:dvrv-in-1:devo/2113v1Epxh:sla/21"
OLD_SLA_IDS = [
    "don:core:dvrv-in-1:devo/2113v1Epxh:sla/20",  # Hero Moto Fincorp Test
    "don:core:dvrv-in-1:devo/2113v1Epxh:sla/19",  # Global Assure test
    "don:core:dvrv-in-1:devo/2113v1Epxh:sla/12",  # Aug-4.08 p.m
    "don:core:dvrv-in-1:devo/2113v1Epxh:sla/9",   # Test 1
    "don:core:dvrv-in-1:devo/2113v1Epxh:sla/2"    # Motor TATA Financial Endorsement
]

def transition_sla_status(sla_id: str, status: str, sla_name: str = "") -> bool:
    """Transition SLA status"""
    print(f"🔄 Transitioning {sla_name} ({sla_id}) to {status}")
    
    transition_payload = {
        "id": sla_id,
        "status": status
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/slas.transition",
            headers=HEADERS,
            json=transition_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            print(f"✅ Successfully transitioned {sla_name} to {status}")
            return True
        else:
            print(f"❌ Failed to transition {sla_name}: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error transitioning {sla_name}: {e}")
        return False

def get_current_slas():
    """Get current SLA status"""
    try:
        # Get published SLAs
        response = requests.get(
            f"{API_BASE_URL}/slas.list?status=published",
            headers=HEADERS,
            timeout=30
        )
        
        published_slas = []
        if response.status_code == 200:
            data = response.json()
            published_slas = data.get('slas', [])
        
        # Get draft SLAs
        response = requests.get(
            f"{API_BASE_URL}/slas.list?status=draft",
            headers=HEADERS,
            timeout=30
        )
        
        draft_slas = []
        if response.status_code == 200:
            data = response.json()
            draft_slas = data.get('slas', [])
        
        return published_slas, draft_slas
        
    except Exception as e:
        print(f"❌ Error getting SLAs: {e}")
        return [], []

def main():
    print("Complete SLA Consolidation")
    print("=" * 50)
    
    # Check current status
    print("\n📋 Checking current SLA status...")
    published_slas, draft_slas = get_current_slas()
    
    print(f"\nPublished SLAs ({len(published_slas)}):")
    for sla in published_slas:
        print(f"  ✅ {sla['name']} - {len(sla.get('policies', []))} policies")
    
    print(f"\nDraft SLAs ({len(draft_slas)}):")
    consolidated_found = False
    for sla in draft_slas:
        print(f"  📝 {sla['name']} - {len(sla.get('policies', []))} policies")
        if "Consolidated" in sla['name']:
            consolidated_found = True
    
    if not consolidated_found:
        print("❌ Consolidated SLA not found in drafts!")
        sys.exit(1)
    
    print(f"\n🎯 Plan:")
    print("1. Publish 'Default SLA (Consolidated)' (9 policies)")
    print("2. Archive all old individual SLAs")
    print("3. Set consolidated SLA as default")
    
    confirm = input("\nProceed with consolidation? (yes/no): ").lower().strip()
    if confirm != 'yes':
        print("❌ Consolidation cancelled.")
        sys.exit(0)
    
    # Step 1: Publish consolidated SLA
    print(f"\n🚀 Step 1: Publishing consolidated SLA...")
    success = transition_sla_status(CONSOLIDATED_SLA_ID, 'published', 'Default SLA (Consolidated)')
    
    if not success:
        print("❌ Failed to publish consolidated SLA. Stopping.")
        sys.exit(1)
    
    # Step 2: Archive old SLAs
    print(f"\n🗄️ Step 2: Archiving old SLAs...")
    archived_count = 0
    
    for sla_id in OLD_SLA_IDS:
        if transition_sla_status(sla_id, 'archived', f'SLA-{sla_id.split("/")[-1]}'):
            archived_count += 1
    
    print(f"\n📊 Results:")
    print(f"✅ Consolidated SLA published")
    print(f"✅ {archived_count}/{len(OLD_SLA_IDS)} old SLAs archived")
    
    if archived_count == len(OLD_SLA_IDS):
        print(f"\n🎉 Consolidation completed successfully!")
        print(f"\nYou now have:")
        print(f"✅ One consolidated SLA with 9 policies")
        print(f"✅ All old individual SLAs archived")
        print(f"\nNext steps:")
        print(f"1. Refresh your SLA page")
        print(f"2. Verify the consolidated SLA is working")
        print(f"3. Test with a sample ticket")
    else:
        print(f"\n⚠️ Some SLAs couldn't be archived. Please check manually.")

if __name__ == "__main__":
    main()
