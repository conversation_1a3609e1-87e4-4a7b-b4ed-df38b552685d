# DevRev to Zendesk Reverse Sync Configuration

# Zendesk Configuration
zendesk:
  base_url: "https://devrev.zendesk.com"
  api_version: "v2"
  auth_type: "Basic"
  # Credentials should be set via environment variables
  # ZENDESK_EMAIL and ZENDESK_API_TOKEN

# DevRev Configuration
devrev:
  base_url: "https://api.dev.devrev-eng.ai"
  # Auth token should be set via environment variable
  # DEVREV_AUTH_TOKEN

# Sync Configuration
sync:
  # Sync unit ID for creating mapper records
  # This should be the ID of an existing sync unit from airdrop
  sync_unit_id: "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758"
  
  # Strategy for handling duplicate contacts
  # Options: "skip" or "update"
  duplicate_strategy: "update"
  
  # Default user role for new Zendesk users
  default_user_role: "end-user"
  
  # Whether to verify new users automatically
  auto_verify_users: true

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s" 