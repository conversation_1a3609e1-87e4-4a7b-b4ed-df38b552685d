#!/usr/bin/env python3
"""
Create Consolidated Default SLA

This script creates a new consolidated SLA containing all policies from existing SLAs
"""

import json
import requests
import sys
from typing import Dict, List, Any

# Configuration
API_BASE_URL = "https://app.devrev.ai/api/gateway/internal"
AUTH_TOKEN = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ind5bTMzT0dhMG84TzdNN200OC1pZCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.c14ljGIzqm731Y5uvWdl_1xFnG77XfrnmQo2LkT5UUekPjg4-4WgfeID_bbbSP8YLL3K3RZdOTRygO0bG98WZPDeAvItxe6EsLHWqThQdjI7MgpXRycRwVYXNpcvJRRbvROcYM4bjy1BJD33o-cZmPxXd3JRc2Iwwk-cDAqSz0XkAeJoNaPCVJhc0Y4NMsTo0HGg2bIQkYetJBhNzQB8RzEgtOYXMvr6cKHIw9H1ScdxKX0O17MScXfbH1vlDdc8zVf0wZfeW3k4__pGP_60IAT3QBWieThMcaysLm2V0vljrnlfV2sMa-wPYOsSwuytQT4QETCvicSd9rk04Wa0zQ'

HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US',
    'authorization': AUTH_TOKEN,
    'content-type': 'application/json',
    'priority': 'u=1, i',
    'referer': 'https://app.devrev.ai/',
    'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
    'x-devrev-client-id': 'ai.devrev.web-product.prod',
    'x-devrev-client-platform': 'web-product',
    'x-devrev-client-version': '7a3380a',
    'x-devrev-dev-user-don': 'don:identity:dvrv-in-1:devo/2113v1Epxh:devu/23'
}

def load_current_slas() -> Dict[str, Any]:
    """Load current SLAs from the JSON file"""
    try:
        with open('current_slas.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: current_slas.json not found. Please run the curl command first.")
        sys.exit(1)

def transform_policy_for_create(policy: Dict[str, Any], source_sla_name: str) -> Dict[str, Any]:
    """Transform policy from current format to set-sla-policy format for creation"""
    transformed_metrics = []

    for metric in policy.get('metrics', []):
        transformed_metric = {
            "metric": metric['metric_definition']['id'],
            "target": metric['target'],
            "performance": metric.get('performance', 0)
        }

        if 'warning_target' in metric:
            transformed_metric['warning_target'] = metric['warning_target']

        # Only include org_schedule if it's not archived
        if 'org_schedule' in metric:
            org_schedule = metric['org_schedule']
            if org_schedule.get('status') != 'archived':
                transformed_metric['org_schedule_id'] = org_schedule['id']
            else:
                print(f"  Skipping archived org_schedule: {org_schedule.get('name', 'Unknown')}")

        transformed_metrics.append(transformed_metric)

    # Transform selector
    selector = policy['selector'].copy()

    # Convert group objects to group IDs
    if 'group' in selector:
        selector['groups'] = [group['id'] for group in selector['group']]
        del selector['group']

    # Convert parts objects to part IDs
    if 'parts' in selector:
        selector['parts'] = [part['id'] for part in selector['parts']]

    # Convert tags objects to tag IDs
    if 'tags' in selector:
        selector['tags'] = [tag['id'] for tag in selector['tags']]

    return {
        "name": f"{source_sla_name} - {policy['name']}",
        "metrics": transformed_metrics,
        "selector": selector
    }

def create_consolidated_sla(slas_data: Dict[str, Any]) -> bool:
    """Create a new consolidated SLA with all policies"""
    print("=== Creating Consolidated Default SLA ===")
    
    slas = slas_data.get('slas', [])
    all_policies = []
    
    # Collect all policies from all SLAs
    for sla in slas:
        sla_name = sla['name']
        print(f"Adding policies from: {sla_name}")
        
        for policy in sla.get('policies', []):
            transformed_policy = transform_policy_for_create(policy, sla_name)
            all_policies.append(transformed_policy)
    
    print(f"Total policies to consolidate: {len(all_policies)}")
    
    # Create the consolidated SLA payload
    create_payload = {
        "name": "Default SLA (Consolidated)",
        "description": "Consolidated SLA containing all policies from Global Assure test, Hero Moto Fincorp Test, Aug-4.08 p.m, Test 1, and Motor TATA Financial Endorsement SLAs",
        "sla_type": "external",
        "applies_to": ["conversation", "ticket"],
        "evaluation_period": "weekly",
        "policies": all_policies
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/slas.create",
            headers=HEADERS,
            json=create_payload,
            timeout=30
        )
        
        if response.status_code == 201:
            print("✅ Successfully created consolidated SLA")
            result = response.json()
            new_sla = result.get('sla', {})
            print(f"New SLA ID: {new_sla.get('id')}")
            print(f"New SLA Name: {new_sla.get('name')}")
            print(f"Status: {new_sla.get('status')}")
            return True
        else:
            print(f"❌ Failed to create consolidated SLA: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error creating consolidated SLA: {e}")
        return False

def transition_sla_status(sla_id: str, status: str) -> bool:
    """Transition SLA status"""
    print(f"Transitioning SLA {sla_id} to {status}")
    
    transition_payload = {
        "id": sla_id,
        "status": status
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/slas.transition",
            headers=HEADERS,
            json=transition_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            print(f"✅ Successfully transitioned SLA to {status}")
            return True
        else:
            print(f"❌ Failed to transition SLA: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error transitioning SLA: {e}")
        return False

def main():
    print("Create Consolidated Default SLA")
    print("=" * 50)
    
    # Load current SLAs
    slas_data = load_current_slas()
    
    print("\nCurrent SLAs to be consolidated:")
    for sla in slas_data.get('slas', []):
        print(f"  - {sla['name']} ({len(sla.get('policies', []))} policies)")
    
    # Confirm before proceeding
    print(f"\n⚠️  This will create a new consolidated SLA with all {sum(len(sla.get('policies', [])) for sla in slas_data.get('slas', []))} policies!")
    
    confirm = input("\nDo you want to proceed? (yes/no): ").lower().strip()
    if confirm != 'yes':
        print("Operation cancelled.")
        sys.exit(0)
    
    # Create consolidated SLA
    success = create_consolidated_sla(slas_data)
    
    if success:
        print("\n🎉 Consolidated SLA created successfully!")
        print("\nNext steps:")
        print("1. Review the new consolidated SLA in the DevRev UI")
        print("2. Publish the new SLA when ready")
        print("3. Archive the old individual SLAs manually if desired")
        print("4. Test that tickets are being assigned to the correct policies")
    else:
        print("\n❌ Failed to create consolidated SLA")

if __name__ == "__main__":
    main()
