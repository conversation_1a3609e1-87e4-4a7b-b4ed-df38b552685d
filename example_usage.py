"""
Example usage of the Reverse Sync Workflow
"""
import os
from reverse_sync_workflow import ReverseSyncWorkflow


def setup_environment():
    """Setup environment variables for testing"""
    # Set these environment variables with your actual credentials
    os.environ['ZENDESK_EMAIL'] = '<EMAIL>'
    os.environ['ZENDESK_API_TOKEN'] = 'your-zendesk-api-token'
    os.environ['DEVREV_AUTH_TOKEN'] = 'your-devrev-auth-token'


def example_ticket_processing():
    """Example of processing a ticket for reverse sync"""
    
    # Initialize the workflow
    workflow = ReverseSyncWorkflow()
    
    # Example ticket data from DevRev
    # This structure should match your actual DevRev ticket format
    example_ticket = {
        "id": "don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1",
        "reported_by": "don:core:dvrv-us-1:devo/1cVRZVoinn:revuser/123",
        "title": "Bug Report: Login Issue",
        "description": "Users are unable to log in to the application",
        "priority": "high",
        "created_at": "2025-01-15T10:30:00Z"
    }
    
    # Process the ticket contact
    result = workflow.process_ticket_contact(example_ticket)
    
    print("=== Reverse Sync Result ===")
    print(f"Status: {result['status']}")
    
    if result['status'] == 'success':
        print(f"Zendesk User ID: {result['zendesk_user_id']}")
        print(f"DevRev User ID: {result['devrev_user_id']}")
        print("Sync mapper record created successfully")
        
    elif result['status'] == 'updated':
        print(f"Zendesk User ID: {result['zendesk_user_id']}")
        print(f"DevRev User ID: {result['devrev_user_id']}")
        print("Existing contact updated and sync mapper record created/updated")
        
    elif result['status'] == 'skipped':
        print(f"Reason: {result['reason']}")
        
    elif result['status'] == 'error':
        print(f"Error: {result['reason']}")


def example_curl_commands():
    """Example curl commands for manual testing"""
    
    print("\n=== Example CURL Commands ===")
    
    # 1. Create Zendesk User
    print("\n1. Create Zendesk User:")
    print("""
curl -X POST \\
  -H "Content-Type: application/json" \\
  -u "<EMAIL>/token:your-api-token" \\
  -d '{
    "user": {
      "name": "John Doe",
      "email": "<EMAIL>",
      "role": "end-user",
      "verified": true
    }
  }' \\
  https://devrev.zendesk.com/api/v2/users.json
    """)
    
    # 2. Create Sync Mapper Record
    print("\n2. Create Sync Mapper Record:")
    print("""
curl -X POST \\
  -H "Content-Type: application/json" \\
  -H "Authorization: your-devrev-auth-token" \\
  -d '{
    "sync_unit": "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758",
    "external_ids": ["39224878322580"],
    "targets": ["don:core:dvrv-us-1:devo/1cVRZVoinn:revuser/123"]
  }' \\
  https://api.dev.devrev-eng.ai/internal/airdrop.sync-mapper-record.create
    """)


def main():
    """Main function to run examples"""
    print("DevRev to Zendesk Reverse Sync Workflow Examples")
    print("=" * 50)
    
    # Uncomment the line below and set your credentials for testing
    # setup_environment()
    
    # Run examples
    try:
        example_ticket_processing()
        example_curl_commands()
        
    except Exception as e:
        print(f"Error running examples: {e}")
        print("\nMake sure to:")
        print("1. Set up your environment variables (ZENDESK_EMAIL, ZENDESK_API_TOKEN, DEVREV_AUTH_TOKEN)")
        print("2. Update the config.yaml file with your sync unit ID")
        print("3. Install required dependencies: pip install -r requirements.txt")


if __name__ == "__main__":
    main() 