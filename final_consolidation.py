#!/usr/bin/env python3
"""
Final SLA Consolidation with your fresh token
"""

import json
import requests
import sys

# Your fresh token
AUTH_TOKEN = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ind5bTMzT0dhMG84TzdNN200OC1pZCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.MGoLzQZHhYGk-JGTm0ECECI_NqphN-sr0U5nyRMHskaxeGPpv6nnp81IxVWscsqq8QN4gEdBdadEwQW31Rb2D44uPsXSDkJubv-A1suUDhFFSdYfC7KkTMGjRpY8XQQfd4nfV5yp1DZBYZfgLWEpAg78D1fEUVnl98-5SeavjzVXRsZ5c3XH3_2Hthq3p9MVxw8Jg8gWnMPoUzq85uSWJ2YwUOOG_HSfTFuVAsw7bKBPs_LHf-cDkxGcDqL-t9J4ZRvQcQrfDIcRyRikZaqzJtkboBEGeyZisZQR9Txq-8IA6PA07Gf5egQv7rrZtwnF5Xgn3ns-Q-L_U1MgtaQCvA'

API_BASE_URL = "https://app.devrev.ai/api/gateway/internal"

HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US',
    'authorization': AUTH_TOKEN,
    'content-type': 'application/json',
    'x-devrev-client-platform': 'web-product',
    'x-devrev-client-version': '7a3380a',
    'x-devrev-dev-user-don': 'don:identity:dvrv-in-1:devo/2113v1Epxh:devu/23'
}

# SLA IDs
CONSOLIDATED_SLA_ID = "don:core:dvrv-in-1:devo/2113v1Epxh:sla/21"
OLD_SLA_IDS = [
    ("don:core:dvrv-in-1:devo/2113v1Epxh:sla/20", "Hero Moto Fincorp Test"),
    ("don:core:dvrv-in-1:devo/2113v1Epxh:sla/19", "Global Assure test"),
    ("don:core:dvrv-in-1:devo/2113v1Epxh:sla/12", "Aug-4.08 p.m"),
    ("don:core:dvrv-in-1:devo/2113v1Epxh:sla/9", "Test 1"),
    ("don:core:dvrv-in-1:devo/2113v1Epxh:sla/2", "Motor TATA Financial Endorsement")
]

def transition_sla_status(sla_id: str, status: str, sla_name: str = "") -> bool:
    """Transition SLA status"""
    print(f"🔄 Transitioning {sla_name} to {status}")
    
    transition_payload = {
        "id": sla_id,
        "status": status
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/slas.transition",
            headers=HEADERS,
            json=transition_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            print(f"✅ Successfully transitioned {sla_name} to {status}")
            return True
        else:
            print(f"❌ Failed to transition {sla_name}: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error transitioning {sla_name}: {e}")
        return False

def get_current_slas():
    """Get current SLA status"""
    try:
        # Get published SLAs
        response = requests.get(
            f"{API_BASE_URL}/slas.list?status=published",
            headers=HEADERS,
            timeout=30
        )
        
        published_slas = []
        if response.status_code == 200:
            data = response.json()
            published_slas = data.get('slas', [])
        
        # Get draft SLAs
        response = requests.get(
            f"{API_BASE_URL}/slas.list?status=draft",
            headers=HEADERS,
            timeout=30
        )
        
        draft_slas = []
        if response.status_code == 200:
            data = response.json()
            draft_slas = data.get('slas', [])
        
        return published_slas, draft_slas
        
    except Exception as e:
        print(f"❌ Error getting SLAs: {e}")
        return [], []

def main():
    print("🎯 Final SLA Consolidation")
    print("=" * 50)
    
    # Check current status
    print("\n📋 Checking current SLA status...")
    published_slas, draft_slas = get_current_slas()
    
    print(f"\nPublished SLAs ({len(published_slas)}):")
    for sla in published_slas:
        print(f"  ✅ {sla['name']} - {len(sla.get('policies', []))} policies")
    
    print(f"\nDraft SLAs ({len(draft_slas)}):")
    consolidated_found = False
    for sla in draft_slas:
        print(f"  📝 {sla['name']} - {len(sla.get('policies', []))} policies")
        if "Consolidated" in sla['name']:
            consolidated_found = True
    
    if not consolidated_found:
        print("❌ Consolidated SLA not found in drafts!")
        return
    
    print(f"\n🎯 Consolidation Plan:")
    print("1. Publish 'Default SLA (Consolidated)' (contains 9 policies)")
    print("2. Archive all old individual SLAs")
    
    confirm = input("\nProceed with consolidation? (yes/no): ").lower().strip()
    if confirm != 'yes':
        print("❌ Consolidation cancelled.")
        return
    
    # Step 1: Publish consolidated SLA
    print(f"\n🚀 Step 1: Publishing consolidated SLA...")
    success = transition_sla_status(CONSOLIDATED_SLA_ID, 'published', 'Default SLA (Consolidated)')
    
    if not success:
        print("❌ Failed to publish consolidated SLA. Stopping.")
        return
    
    # Step 2: Archive old SLAs
    print(f"\n🗄️ Step 2: Archiving old SLAs...")
    archived_count = 0
    
    for sla_id, sla_name in OLD_SLA_IDS:
        if transition_sla_status(sla_id, 'archived', sla_name):
            archived_count += 1
    
    print(f"\n📊 Final Results:")
    print(f"✅ Consolidated SLA published successfully")
    print(f"✅ {archived_count}/{len(OLD_SLA_IDS)} old SLAs archived")
    
    if archived_count == len(OLD_SLA_IDS):
        print(f"\n🎉 CONSOLIDATION COMPLETED SUCCESSFULLY!")
        print(f"\n✨ You now have:")
        print(f"   📋 ONE consolidated SLA with 9 policies")
        print(f"   🗄️ All old individual SLAs archived")
        print(f"\n🔄 Please refresh your SLA page to see the changes!")
    else:
        print(f"\n⚠️ Some SLAs couldn't be archived. Please check manually.")

if __name__ == "__main__":
    main()
