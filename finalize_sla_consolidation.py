#!/usr/bin/env python3
"""
Finalize SLA Consolidation

This script:
1. Publishes the new consolidated SLA
2. Optionally archives the old individual SLAs
"""

import json
import requests
import sys
from typing import Dict, List, Any

# Configuration
API_BASE_URL = "https://app.devrev.ai/api/gateway/internal"
AUTH_TOKEN = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ind5bTMzT0dhMG84TzdNN200OC1pZCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.c14ljGIzqm731Y5uvWdl_1xFnG77XfrnmQo2LkT5UUekPjg4-4WgfeID_bbbSP8YLL3K3RZdOTRygO0bG98WZPDeAvItxe6EsLHWqThQdjI7MgpXRycRwVYXNpcvJRRbvROcYM4bjy1BJD33o-cZmPxXd3JRc2Iwwk-cDAqSz0XkAeJoNaPCVJhc0Y4NMsTo0HGg2bIQkYetJBhNzQB8RzEgtOYXMvr6cKHIw9H1ScdxKX0O17MScXfbH1vlDdc8zVf0wZfeW3k4__pGP_60IAT3QBWieThMcaysLm2V0vljrnlfV2sMa-wPYOsSwuytQT4QETCvicSd9rk04Wa0zQ'

HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US',
    'authorization': AUTH_TOKEN,
    'content-type': 'application/json',
    'priority': 'u=1, i',
    'referer': 'https://app.devrev.ai/',
    'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
    'x-devrev-client-id': 'ai.devrev.web-product.prod',
    'x-devrev-client-platform': 'web-product',
    'x-devrev-client-version': '7a3380a',
    'x-devrev-dev-user-don': 'don:identity:dvrv-in-1:devo/2113v1Epxh:devu/23'
}

# SLA IDs from the previous operations
CONSOLIDATED_SLA_ID = "don:core:dvrv-in-1:devo/2113v1Epxh:sla/21"
OLD_SLA_IDS = [
    ("don:core:dvrv-in-1:devo/2113v1Epxh:sla/20", "Hero Moto Fincorp Test"),
    ("don:core:dvrv-in-1:devo/2113v1Epxh:sla/19", "Global Assure test"),
    ("don:core:dvrv-in-1:devo/2113v1Epxh:sla/12", "Aug-4.08 p.m"),
    ("don:core:dvrv-in-1:devo/2113v1Epxh:sla/9", "Test 1"),
    ("don:core:dvrv-in-1:devo/2113v1Epxh:sla/2", "Motor TATA Financial Endorsement")
]

def transition_sla_status(sla_id: str, status: str, sla_name: str = "") -> bool:
    """Transition SLA status"""
    print(f"Transitioning SLA {sla_name} ({sla_id}) to {status}")
    
    transition_payload = {
        "id": sla_id,
        "status": status
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/slas.transition",
            headers=HEADERS,
            json=transition_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            print(f"✅ Successfully transitioned {sla_name} to {status}")
            return True
        else:
            print(f"❌ Failed to transition {sla_name}: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error transitioning {sla_name}: {e}")
        return False

def main():
    print("Finalize SLA Consolidation")
    print("=" * 50)
    
    print(f"\nConsolidated SLA ID: {CONSOLIDATED_SLA_ID}")
    print(f"Old SLAs to potentially archive: {len(OLD_SLA_IDS)}")
    
    # Step 1: Publish the consolidated SLA
    print("\n=== Step 1: Publishing Consolidated SLA ===")
    publish_success = transition_sla_status(CONSOLIDATED_SLA_ID, 'published', 'Default SLA (Consolidated)')
    
    if not publish_success:
        print("Failed to publish consolidated SLA. Stopping.")
        sys.exit(1)
    
    # Step 2: Ask about archiving old SLAs
    print("\n=== Step 2: Archive Old SLAs ===")
    print("Old SLAs that can be archived:")
    for sla_id, sla_name in OLD_SLA_IDS:
        print(f"  - {sla_name} ({sla_id})")
    
    archive_confirm = input("\nDo you want to archive the old SLAs? (yes/no): ").lower().strip()
    
    if archive_confirm == 'yes':
        archived_count = 0
        for sla_id, sla_name in OLD_SLA_IDS:
            if transition_sla_status(sla_id, 'archived', sla_name):
                archived_count += 1
        
        print(f"\n✅ Successfully archived {archived_count}/{len(OLD_SLA_IDS)} SLAs")
    else:
        print("\nSkipping archival of old SLAs. You can archive them manually later if needed.")
    
    # Final summary
    print(f"\n=== Final Summary ===")
    print("✅ Consolidated SLA created and published successfully!")
    print(f"✅ New SLA ID: {CONSOLIDATED_SLA_ID}")
    print("✅ Contains 9 policies from all previous SLAs:")
    print("   - 3 policies from Hero Moto Fincorp Test")
    print("   - 3 policies from Global Assure test") 
    print("   - 1 policy from Aug-4.08 p.m")
    print("   - 1 policy from Test 1")
    print("   - 1 policy from Motor TATA Financial Endorsement")
    
    if archive_confirm == 'yes':
        print("✅ Old SLAs have been archived")
    
    print(f"\n🎉 SLA consolidation completed successfully!")
    print("\nNext steps:")
    print("1. Verify the consolidated SLA in the DevRev UI")
    print("2. Test that tickets are being assigned to the correct policies")
    print("3. Monitor SLA performance metrics")
    print("4. Adjust policy priorities if needed")

if __name__ == "__main__":
    main()
