#!/usr/bin/env python3
"""
Find Default SLA by checking around Bajaj Motor position
"""

import json

def find_default_by_position():
    """Find Default SLA by checking around Bajaj Motor position"""
    try:
        with open('production_slas.json', 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("❌ production_slas.json not found")
        return
    
    slas = data.get('slas', [])
    
    print(f"🔍 LOOKING FOR DEFAULT SLA BEFORE BAJAJ MOTOR")
    print("=" * 60)
    
    # Find Bajaj Motor position
    bajaj_position = -1
    for i, sla in enumerate(slas):
        if 'bajaj' in sla['name'].lower() and 'motor' in sla['name'].lower():
            bajaj_position = i
            print(f"📍 Found Bajaj Motor at position {i+1}: {sla['name']}")
            break
    
    if bajaj_position == -1:
        print("❌ Bajaj Motor SLA not found")
        return
    
    # Show SLAs around Bajaj position
    start_pos = max(0, bajaj_position - 10)
    end_pos = min(len(slas), bajaj_position + 3)
    
    print(f"\n📋 SLAs AROUND BAJAJ MOTOR (positions {start_pos+1} to {end_pos}):")
    
    default_sla = None
    
    for i in range(start_pos, end_pos):
        sla = slas[i]
        name = sla['name']
        policies = len(sla.get('policies', []))
        status = sla['status']
        
        # Highlight if this might be the Default SLA
        marker = ""
        if name.lower() == 'default' or 'default' in name.lower():
            marker = " ⭐ POTENTIAL DEFAULT SLA"
            default_sla = sla
        elif policies <= 2 and i < bajaj_position:
            marker = " 🤔 MINIMAL POLICIES (could be default)"
        
        print(f"{i+1:2d}. {name} ({policies} policies, {status}){marker}")
    
    # If we found a potential default SLA, show details
    if default_sla:
        print(f"\n🎯 DEFAULT SLA DETAILS:")
        print(f"Name: {default_sla['name']}")
        print(f"ID: {default_sla['id']}")
        print(f"Status: {default_sla['status']}")
        print(f"Policies: {len(default_sla.get('policies', []))}")
        
        # Show policies
        for i, policy in enumerate(default_sla.get('policies', []), 1):
            print(f"\n{i}. {policy['name']}")
            for metric in policy.get('metrics', []):
                metric_name = metric['metric_definition']['name']
                target = metric['target']
                print(f"   - {metric_name}: {target}min")
        
        # Save the Default SLA
        with open('found_default_sla.json', 'w') as f:
            json.dump(default_sla, f, indent=2)
        print(f"\n✅ Default SLA saved to found_default_sla.json")
    else:
        print(f"\n❌ No obvious Default SLA found before Bajaj Motor")
        print(f"Please check the SLA list manually or provide the exact name")

if __name__ == "__main__":
    find_default_by_position()
