#!/usr/bin/env python3
"""
Find and analyze the Default SLA
"""

import json

def find_default_sla():
    """Find and analyze the Default SLA"""
    try:
        with open('production_slas.json', 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("❌ production_slas.json not found")
        return
    
    slas = data.get('slas', [])
    
    # Look for Default SLA
    default_sla = None
    for sla in slas:
        if sla['name'].lower() == 'default':
            default_sla = sla
            break
    
    if not default_sla:
        print("❌ No Default SLA found!")
        print("Available SLAs:")
        for sla in slas[:10]:  # Show first 10
            print(f"  - {sla['name']}")
        return
    
    print("🎯 DEFAULT SLA FOUND!")
    print("=" * 50)
    print(f"Name: {default_sla['name']}")
    print(f"ID: {default_sla['id']}")
    print(f"Status: {default_sla['status']}")
    print(f"Policies: {len(default_sla.get('policies', []))}")
    
    if default_sla.get('description'):
        print(f"Description: {default_sla['description']}")
    
    # Show policies
    policies = default_sla.get('policies', [])
    if policies:
        print(f"\n📋 CURRENT POLICIES ({len(policies)}):")
        for i, policy in enumerate(policies, 1):
            print(f"\n{i}. {policy['name']}")
            
            # Show metrics
            metrics = policy.get('metrics', [])
            if metrics:
                print("   Metrics:")
                for metric in metrics:
                    metric_name = metric['metric_definition']['name']
                    target = metric['target']
                    print(f"     - {metric_name}: {target}min")
            
            # Show selector
            selector = policy.get('selector', {})
            print("   Selector:")
            print(f"     - Applies to: {selector.get('applies_to', 'N/A')}")
            
            if selector.get('custom_fields'):
                print(f"     - Custom fields: {len(selector['custom_fields'])}")
            if selector.get('group'):
                groups = [g['name'] for g in selector['group']]
                print(f"     - Groups: {', '.join(groups)}")
            if selector.get('parts'):
                parts = [p['name'] for p in selector['parts']]
                print(f"     - Parts: {', '.join(parts)}")
            if selector.get('tags'):
                tags = [t['name'] for t in selector['tags']]
                print(f"     - Tags: {', '.join(tags)}")
    
    # Save just the Default SLA for analysis
    with open('default_sla_only.json', 'w') as f:
        json.dump(default_sla, f, indent=2)
    
    print(f"\n✅ Default SLA saved to default_sla_only.json")

if __name__ == "__main__":
    find_default_sla()
