#!/usr/bin/env python3
"""
Fix the JSON file from curl response
"""

import json

# The raw JSON response from curl
raw_json = """{"slas":[{"applies_to":["conversation","ticket"],"created_by":{"type":"dev_user","display_handle":"i-yuvaraj-s","display_id":"DEVU-131","display_name":"i-yuvaraj-s","email":"<EMAIL>","full_name":"I Yuvaraj S","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:devu/131","id_v1":"don:DEV-2113v1Epxh:dev_user:DEVU-131","state":"active","thumbnail":"https://api.devrev.ai/internal/display-picture/I%20Yuvaraj%20S.png"},"created_date":"2025-09-25T07:22:29.259Z","display_id":"sla-20","evaluation_period":"weekly","id":"don:core:dvrv-in-1:devo/2113v1Epxh:sla/20","modified_by":{"type":"dev_user","display_handle":"poorna-ad","display_id":"DEVU-10","display_name":"poorna-ad","email":"<EMAIL>","full_name":"Poorna Ad","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:devu/10","id_v1":"don:DEV-2113v1Epxh:dev_user:DEVU-10","state":"active","thumbnail":"https://api.devrev.ai/internal/display-picture/Poorna%20Ad.png"},"modified_date":"2025-09-25T08:15:33.276Z","name":"Hero Moto Fincorp Test","policies":[{"metrics":[{"metric_definition":{"display_name":"First response","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1","metric_type":"time","name":"First response","status":"active"},"performance":0,"target":30,"warning_target":15},{"metric_definition":{"display_name":"Resolution time","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3","metric_type":"time","name":"Resolution time","status":"active"},"performance":0,"target":1620,"warning_target":810}],"name":"New ticket policy","selector":{"applies_to":"ticket","custom_fields":{"tnt__insurer_name":"Hero Moto Fincorp","tnt__service_request_type":"Claim Status"},"group":[{"display_id":"group-1","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:group/1","id_v1":"don:DEV-2113v1Epxh:group:1","member_type":"dev_user","name":"Motor SM"}],"tag_operation":"any"}},{"metrics":[{"metric_definition":{"display_name":"First response","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1","metric_type":"time","name":"First response","status":"active"},"performance":0,"target":30,"warning_target":15},{"metric_definition":{"display_name":"Resolution time","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3","metric_type":"time","name":"Resolution time","status":"active"},"performance":0,"target":180,"warning_target":90}],"name":"New ticket policy","selector":{"applies_to":"ticket","custom_fields":{"tnt__insurer_name":"Hero Moto Fincorp","tnt__service_request_type":"Claim Intimation"},"group":[{"display_id":"group-1","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:group/1","id_v1":"don:DEV-2113v1Epxh:group:1","member_type":"dev_user","name":"Motor SM"}],"tag_operation":"any"}},{"metrics":[{"metric_definition":{"display_name":"First response","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1","metric_type":"time","name":"First response","status":"active"},"performance":0,"target":30,"warning_target":15},{"metric_definition":{"display_name":"Resolution time","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3","metric_type":"time","name":"Resolution time","status":"active"},"performance":0,"target":1620,"warning_target":810}],"name":"New ticket policy","selector":{"applies_to":"ticket","custom_fields":{"tnt__insurer_name":"Hero Moto Fincorp","tnt__service_request_type":"Claim Query"},"group":[{"display_id":"group-1","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:group/1","id_v1":"don:DEV-2113v1Epxh:group:1","member_type":"dev_user","name":"Motor SM"}],"tag_operation":"any"}}],"sla_type":"external","status":"published"},{"applies_to":["conversation","ticket"],"created_by":{"type":"dev_user","display_handle":"i-yuvaraj-s","display_id":"DEVU-131","display_name":"i-yuvaraj-s","email":"<EMAIL>","full_name":"I Yuvaraj S","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:devu/131","id_v1":"don:DEV-2113v1Epxh:dev_user:DEVU-131","state":"active","thumbnail":"https://api.devrev.ai/internal/display-picture/I%20Yuvaraj%20S.png"},"created_date":"2025-09-25T07:19:09.55Z","description":"","display_id":"sla-19","evaluation_period":"weekly","id":"don:core:dvrv-in-1:devo/2113v1Epxh:sla/19","modified_by":{"type":"dev_user","display_handle":"poorna-ad","display_id":"DEVU-10","display_name":"poorna-ad","email":"<EMAIL>","full_name":"Poorna Ad","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:devu/10","id_v1":"don:DEV-2113v1Epxh:dev_user:DEVU-10","state":"active","thumbnail":"https://api.devrev.ai/internal/display-picture/Poorna%20Ad.png"},"modified_date":"2025-09-25T08:15:46.997Z","name":"Global Assure test","policies":[{"metrics":[{"metric_definition":{"display_name":"First response","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1","metric_type":"time","name":"First response","status":"active"},"performance":0,"target":30,"warning_target":15},{"metric_definition":{"display_name":"Resolution time","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3","metric_type":"time","name":"Resolution time","status":"active"},"performance":0,"target":1620,"warning_target":810}],"name":"New ticket policy","selector":{"applies_to":"ticket","custom_fields":{"tnt__insurer_name":"Global Assure","tnt__service_request_type":"Claim Query"},"group":[{"display_id":"group-1","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:group/1","id_v1":"don:DEV-2113v1Epxh:group:1","member_type":"dev_user","name":"Motor SM"}],"tag_operation":"any"}},{"metrics":[{"metric_definition":{"display_name":"First response","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1","metric_type":"time","name":"First response","status":"active"},"performance":0,"target":30,"warning_target":15},{"metric_definition":{"display_name":"Resolution time","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3","metric_type":"time","name":"Resolution time","status":"active"},"performance":0,"target":8640,"warning_target":4320}],"name":"New ticket policy","selector":{"applies_to":"ticket","custom_fields":{"tnt__insurer_name":"Global Assure","tnt__service_request_type":"Claim Status"},"group":[{"display_id":"group-3","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:group/3","id_v1":"don:DEV-2113v1Epxh:group:3","member_type":"dev_user","name":"Health Claims"}],"tag_operation":"any"}},{"metrics":[{"metric_definition":{"display_name":"First response","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1","metric_type":"time","name":"First response","status":"active"},"performance":0,"target":30,"warning_target":15},{"metric_definition":{"display_name":"Resolution time","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3","metric_type":"time","name":"Resolution time","status":"active"},"performance":0,"target":180,"warning_target":90}],"name":"New ticket policy","selector":{"applies_to":"ticket","custom_fields":{"tnt__insurer_name":"Global Assure","tnt__service_request_type":"Claim Intimation"},"group":[{"display_id":"group-1","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:group/1","id_v1":"don:DEV-2113v1Epxh:group:1","member_type":"dev_user","name":"Motor SM"}],"tag_operation":"any"}}],"sla_type":"external","status":"published"},{"applies_to":["conversation","ticket"],"created_by":{"type":"dev_user","display_handle":"c-adhithya-sankar","display_id":"DEVU-17","display_name":"c-adhithya-sankar","email":"<EMAIL>","full_name":"C Adhithya Sankar","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:devu/17","id_v1":"don:DEV-2113v1Epxh:dev_user:DEVU-17","state":"active","thumbnail":"https://api.devrev.ai/internal/display-picture/C%20Adhithya%20Sankar.png"},"created_date":"2025-08-08T10:38:26.054Z","description":"**\u003cu\u003etest\u003c/u\u003e**","display_id":"sla-12","evaluation_period":"weekly","id":"don:core:dvrv-in-1:devo/2113v1Epxh:sla/12","modified_by":{"type":"dev_user","display_handle":"c-adhithya-sankar","display_id":"DEVU-17","display_name":"c-adhithya-sankar","email":"<EMAIL>","full_name":"C Adhithya Sankar","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:devu/17","id_v1":"don:DEV-2113v1Epxh:dev_user:DEVU-17","state":"active","thumbnail":"https://api.devrev.ai/internal/display-picture/C%20Adhithya%20Sankar.png"},"modified_date":"2025-08-08T11:09:56.563Z","name":"Aug-4.08 p.m","policies":[{"metrics":[{"metric_definition":{"display_name":"First response","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1","metric_type":"time","name":"First response","status":"active"},"performance":0,"target":2,"warning_target":1},{"metric_definition":{"display_name":"Next response","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/2","metric_type":"time","name":"Next response","status":"active"},"performance":0,"target":2,"warning_target":1},{"metric_definition":{"display_name":"Resolution time","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3","metric_type":"time","name":"Resolution time","status":"active"},"performance":0,"target":4,"warning_target":3}],"name":"New ticket policy","selector":{"applies_to":"ticket","custom_fields":{},"parts":[{"type":"feature","display_id":"FEAT-17","id":"don:core:dvrv-in-1:devo/2113v1Epxh:feature/17","id_v1":"don:DEV-2113v1Epxh:feature:17","name":"Family Floater","owned_by":[{"type":"dev_user","display_handle":"jeet-yadav","display_id":"DEVU-3","display_name":"jeet-yadav","email":"<EMAIL>","full_name":"Jeet Yadav","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:devu/3","id_v1":"don:DEV-2113v1Epxh:dev_user:DEVU-3","state":"active","thumbnail":"https://api.devrev.ai/internal/display-picture/Jeet%20Yadav.png"}],"stage":{"name":""}}],"severity":["high"],"subtype":["claim_intimation"],"tag_operation":"any","tags":[{"display_id":"TAG-23","id":"don:core:dvrv-in-1:devo/2113v1Epxh:tag/23","id_v1":"don:DEV-2113v1Epxh:tag:23","name":"Aug8-newtagtest-update","style":"{\"color\":{\"code\":\"#31D7F7\",\"name\":\"Sky blue\"}}","style_new":{"color":"#31D7F7"}}]}}],"sla_type":"external","status":"published"},{"applies_to":["conversation","ticket"],"created_by":{"type":"dev_user","display_handle":"jeet-yadav","display_id":"DEVU-3","display_name":"jeet-yadav","email":"<EMAIL>","full_name":"Jeet Yadav","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:devu/3","id_v1":"don:DEV-2113v1Epxh:dev_user:DEVU-3","state":"active","thumbnail":"https://api.devrev.ai/internal/display-picture/Jeet%20Yadav.png"},"created_date":"2025-06-12T11:22:20.139Z","description":"For testing","display_id":"sla-9","evaluation_period":"weekly","id":"don:core:dvrv-in-1:devo/2113v1Epxh:sla/9","modified_by":{"type":"dev_user","display_handle":"jeet-yadav","display_id":"DEVU-3","display_name":"jeet-yadav","email":"<EMAIL>","full_name":"Jeet Yadav","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:devu/3","id_v1":"don:DEV-2113v1Epxh:dev_user:DEVU-3","state":"active","thumbnail":"https://api.devrev.ai/internal/display-picture/Jeet%20Yadav.png"},"modified_date":"2025-06-12T11:29:20.242Z","name":"Test 1","policies":[{"metrics":[{"metric_definition":{"display_name":"First response","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1","metric_type":"time","name":"First response","status":"active"},"org_schedule":{"display_id":"org_schedule-4","id":"don:core:dvrv-in-1:devo/2113v1Epxh:org_schedule/4","name":"CST ","status":"archived","timezone":"Asia/Kolkata","valid_until":"2026-05-31T00:00:00Z"},"performance":0,"target":30,"warning_target":0},{"metric_definition":{"display_name":"Resolution time","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3","metric_type":"time","name":"Resolution time","status":"active"},"org_schedule":{"display_id":"org_schedule-4","id":"don:core:dvrv-in-1:devo/2113v1Epxh:org_schedule/4","name":"CST ","status":"archived","timezone":"Asia/Kolkata","valid_until":"2026-05-31T00:00:00Z"},"performance":0,"target":15,"warning_target":0}],"name":"New ticket policy","selector":{"applies_to":"ticket","custom_fields":{"tnt__service_request_type":"Claims","tnt__test":"Yes"},"group":[{"display_id":"group-2","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:group/2","id_v1":"don:DEV-2113v1Epxh:group:2","member_type":"dev_user","name":"Motor Claims"}],"tag_operation":"any"}}],"sla_type":"external","status":"published"},{"applies_to":["conversation","ticket"],"created_by":{"type":"dev_user","display_handle":"jeet-yadav","display_id":"DEVU-3","display_name":"jeet-yadav","email":"<EMAIL>","full_name":"Jeet Yadav","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:devu/3","id_v1":"don:DEV-2113v1Epxh:dev_user:DEVU-3","state":"active","thumbnail":"https://api.devrev.ai/internal/display-picture/Jeet%20Yadav.png"},"created_date":"2025-05-28T12:32:54.749Z","display_id":"sla-2","evaluation_period":"weekly","id":"don:core:dvrv-in-1:devo/2113v1Epxh:sla/2","modified_by":{"type":"dev_user","display_handle":"jeet-yadav","display_id":"DEVU-3","display_name":"jeet-yadav","email":"<EMAIL>","full_name":"Jeet Yadav","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:devu/3","id_v1":"don:DEV-2113v1Epxh:dev_user:DEVU-3","state":"active","thumbnail":"https://api.devrev.ai/internal/display-picture/Jeet%20Yadav.png"},"modified_date":"2025-05-28T12:39:57.615Z","name":"Motor TATA Financial Endorsement","policies":[{"metrics":[{"metric_definition":{"display_name":"First response","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1","metric_type":"time","name":"First response","status":"active"},"org_schedule":{"display_id":"org_schedule-4","id":"don:core:dvrv-in-1:devo/2113v1Epxh:org_schedule/4","name":"CST ","status":"archived","timezone":"Asia/Kolkata","valid_until":"2026-05-31T00:00:00Z"},"performance":0,"target":30,"warning_target":0},{"metric_definition":{"display_name":"Resolution time","id":"don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3","metric_type":"time","name":"Resolution time","status":"active"},"org_schedule":{"display_id":"org_schedule-4","id":"don:core:dvrv-in-1:devo/2113v1Epxh:org_schedule/4","name":"CST ","status":"archived","timezone":"Asia/Kolkata","valid_until":"2026-05-31T00:00:00Z"},"performance":0,"target":8100,"warning_target":7620}],"name":"New ticket policy","selector":{"applies_to":"ticket","custom_fields":{"tnt__service_request_type":"Endorsement"},"group":[{"display_id":"group-1","id":"don:identity:dvrv-in-1:devo/2113v1Epxh:group/1","id_v1":"don:DEV-2113v1Epxh:group:1","member_type":"dev_user","name":"Motor SM"}],"tag_operation":"any"}}],"sla_type":"external","status":"published"}]}"""

# Parse and save as properly formatted JSON
try:
    data = json.loads(raw_json)
    with open('current_slas.json', 'w') as f:
        json.dump(data, f, indent=2)
    print("Successfully created current_slas.json")
except json.JSONDecodeError as e:
    print(f"Error parsing JSON: {e}")
