{"slas": [{"applies_to": ["conversation", "ticket"], "created_by": {"type": "dev_user", "display_handle": "sh<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "display_id": "DEVU-23", "display_name": "sh<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "email": "shwethaa.srini<PERSON><EMAIL>", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "don:identity:dvrv-in-1:devo/2113v1Epxh:devu/23", "id_v1": "don:DEV-2113v1Epxh:dev_user:DEVU-23", "state": "active", "thumbnail": "https://api.devrev.ai/internal/display-picture/Shwethaa%20Srinivasan.png"}, "created_date": "2025-09-25T16:58:49.751Z", "description": "Consolidated SLA containing all policies from Global Assure test, Hero Moto Fincorp Test, Aug-4.08 p.m, Test 1, and Motor TATA Financial Endorsement SLAs", "display_id": "sla-21", "evaluation_period": "weekly", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:sla/21", "modified_by": {"type": "dev_user", "display_handle": "sh<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "display_id": "DEVU-23", "display_name": "sh<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "email": "shwethaa.srini<PERSON><EMAIL>", "full_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "id": "don:identity:dvrv-in-1:devo/2113v1Epxh:devu/23", "id_v1": "don:DEV-2113v1Epxh:dev_user:DEVU-23", "state": "active", "thumbnail": "https://api.devrev.ai/internal/display-picture/Shwethaa%20Srinivasan.png"}, "modified_date": "2025-09-25T17:53:37.771Z", "name": "Default SLA (Consolidated)", "policies": [{"metrics": [{"metric_definition": {"display_name": "First response", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1", "metric_type": "time", "name": "First response", "status": "active"}, "performance": 0, "target": 30, "warning_target": 15}, {"metric_definition": {"display_name": "Resolution time", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3", "metric_type": "time", "name": "Resolution time", "status": "active"}, "performance": 0, "target": 1620, "warning_target": 810}], "name": "<PERSON>to Fincorp Test - New ticket policy", "selector": {"applies_to": "ticket", "custom_fields": {"tnt__insurer_name": "Hero <PERSON> Fincorp", "tnt__service_request_type": "Claim Status"}, "group": [{"display_id": "group-1", "id": "don:identity:dvrv-in-1:devo/2113v1Epxh:group/1", "id_v1": "don:DEV-2113v1Epxh:group:1", "member_type": "dev_user", "name": "Motor SM"}], "tag_operation": "any"}}, {"metrics": [{"metric_definition": {"display_name": "First response", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1", "metric_type": "time", "name": "First response", "status": "active"}, "performance": 0, "target": 30, "warning_target": 15}, {"metric_definition": {"display_name": "Resolution time", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3", "metric_type": "time", "name": "Resolution time", "status": "active"}, "performance": 0, "target": 180, "warning_target": 90}], "name": "<PERSON>to Fincorp Test - New ticket policy", "selector": {"applies_to": "ticket", "custom_fields": {"tnt__insurer_name": "Hero <PERSON> Fincorp", "tnt__service_request_type": "Claim Intimation"}, "group": [{"display_id": "group-1", "id": "don:identity:dvrv-in-1:devo/2113v1Epxh:group/1", "id_v1": "don:DEV-2113v1Epxh:group:1", "member_type": "dev_user", "name": "Motor SM"}], "tag_operation": "any"}}, {"metrics": [{"metric_definition": {"display_name": "First response", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1", "metric_type": "time", "name": "First response", "status": "active"}, "performance": 0, "target": 30, "warning_target": 15}, {"metric_definition": {"display_name": "Resolution time", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3", "metric_type": "time", "name": "Resolution time", "status": "active"}, "performance": 0, "target": 1620, "warning_target": 810}], "name": "<PERSON>to Fincorp Test - New ticket policy", "selector": {"applies_to": "ticket", "custom_fields": {"tnt__insurer_name": "Hero <PERSON> Fincorp", "tnt__service_request_type": "<PERSON><PERSON><PERSON>"}, "group": [{"display_id": "group-1", "id": "don:identity:dvrv-in-1:devo/2113v1Epxh:group/1", "id_v1": "don:DEV-2113v1Epxh:group:1", "member_type": "dev_user", "name": "Motor SM"}], "tag_operation": "any"}}, {"metrics": [{"metric_definition": {"display_name": "First response", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1", "metric_type": "time", "name": "First response", "status": "active"}, "performance": 0, "target": 30, "warning_target": 15}, {"metric_definition": {"display_name": "Resolution time", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3", "metric_type": "time", "name": "Resolution time", "status": "active"}, "performance": 0, "target": 1620, "warning_target": 810}], "name": "Global Assure test - New ticket policy", "selector": {"applies_to": "ticket", "custom_fields": {"tnt__insurer_name": "Global Assure", "tnt__service_request_type": "<PERSON><PERSON><PERSON>"}, "group": [{"display_id": "group-1", "id": "don:identity:dvrv-in-1:devo/2113v1Epxh:group/1", "id_v1": "don:DEV-2113v1Epxh:group:1", "member_type": "dev_user", "name": "Motor SM"}], "tag_operation": "any"}}, {"metrics": [{"metric_definition": {"display_name": "First response", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1", "metric_type": "time", "name": "First response", "status": "active"}, "performance": 0, "target": 30, "warning_target": 15}, {"metric_definition": {"display_name": "Resolution time", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3", "metric_type": "time", "name": "Resolution time", "status": "active"}, "performance": 0, "target": 8640, "warning_target": 4320}], "name": "Global Assure test - New ticket policy", "selector": {"applies_to": "ticket", "custom_fields": {"tnt__insurer_name": "Global Assure", "tnt__service_request_type": "Claim Status"}, "group": [{"display_id": "group-3", "id": "don:identity:dvrv-in-1:devo/2113v1Epxh:group/3", "id_v1": "don:DEV-2113v1Epxh:group:3", "member_type": "dev_user", "name": "Health Claims"}], "tag_operation": "any"}}, {"metrics": [{"metric_definition": {"display_name": "First response", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1", "metric_type": "time", "name": "First response", "status": "active"}, "performance": 0, "target": 30, "warning_target": 15}, {"metric_definition": {"display_name": "Resolution time", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3", "metric_type": "time", "name": "Resolution time", "status": "active"}, "performance": 0, "target": 180, "warning_target": 90}], "name": "Global Assure test - New ticket policy", "selector": {"applies_to": "ticket", "custom_fields": {"tnt__insurer_name": "Global Assure", "tnt__service_request_type": "Claim Intimation"}, "group": [{"display_id": "group-1", "id": "don:identity:dvrv-in-1:devo/2113v1Epxh:group/1", "id_v1": "don:DEV-2113v1Epxh:group:1", "member_type": "dev_user", "name": "Motor SM"}], "tag_operation": "any"}}, {"metrics": [{"metric_definition": {"display_name": "First response", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1", "metric_type": "time", "name": "First response", "status": "active"}, "performance": 0, "target": 2, "warning_target": 1}, {"metric_definition": {"display_name": "Next response", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/2", "metric_type": "time", "name": "Next response", "status": "active"}, "performance": 0, "target": 2, "warning_target": 1}, {"metric_definition": {"display_name": "Resolution time", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3", "metric_type": "time", "name": "Resolution time", "status": "active"}, "performance": 0, "target": 4, "warning_target": 3}], "name": "Aug-4.08 p.m - New ticket policy", "selector": {"applies_to": "ticket", "custom_fields": {}, "parts": [{"type": "feature", "display_id": "FEAT-17", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:feature/17", "id_v1": "don:DEV-2113v1Epxh:feature:17", "name": "Family Floater", "owned_by": [{"type": "dev_user", "display_handle": "jeet-yadav", "display_id": "DEVU-3", "display_name": "jeet-yadav", "email": "<EMAIL>", "full_name": "<PERSON><PERSON>", "id": "don:identity:dvrv-in-1:devo/2113v1Epxh:devu/3", "id_v1": "don:DEV-2113v1Epxh:dev_user:DEVU-3", "state": "active", "thumbnail": "https://api.devrev.ai/internal/display-picture/Jeet%20Yadav.png"}], "stage": {"name": ""}}], "severity": ["high"], "subtype": ["claim_intimation"], "tag_operation": "any", "tags": [{"display_id": "TAG-23", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:tag/23", "id_v1": "don:DEV-2113v1Epxh:tag:23", "name": "Aug8-newtagtest-update", "style": "{\"color\":{\"code\":\"#31D7F7\",\"name\":\"Sky blue\"}}", "style_new": {"color": "#31D7F7"}}]}}, {"metrics": [{"metric_definition": {"display_name": "First response", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1", "metric_type": "time", "name": "First response", "status": "active"}, "performance": 0, "target": 30, "warning_target": 0}, {"metric_definition": {"display_name": "Resolution time", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3", "metric_type": "time", "name": "Resolution time", "status": "active"}, "performance": 0, "target": 15, "warning_target": 0}], "name": "Test 1 - New ticket policy", "selector": {"applies_to": "ticket", "custom_fields": {"tnt__service_request_type": "<PERSON><PERSON><PERSON>", "tnt__test": "Yes"}, "group": [{"display_id": "group-2", "id": "don:identity:dvrv-in-1:devo/2113v1Epxh:group/2", "id_v1": "don:DEV-2113v1Epxh:group:2", "member_type": "dev_user", "name": "Motor Claims"}], "tag_operation": "any"}}, {"metrics": [{"metric_definition": {"display_name": "First response", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/1", "metric_type": "time", "name": "First response", "status": "active"}, "performance": 0, "target": 30, "warning_target": 0}, {"metric_definition": {"display_name": "Resolution time", "id": "don:core:dvrv-in-1:devo/2113v1Epxh:metric_definition/3", "metric_type": "time", "name": "Resolution time", "status": "active"}, "performance": 0, "target": 8100, "warning_target": 7620}], "name": "Motor TATA Financial Endorsement - New ticket policy", "selector": {"applies_to": "ticket", "custom_fields": {"tnt__service_request_type": "Endorsement"}, "group": [{"display_id": "group-1", "id": "don:identity:dvrv-in-1:devo/2113v1Epxh:group/1", "id_v1": "don:DEV-2113v1Epxh:group:1", "member_type": "dev_user", "name": "Motor SM"}], "tag_operation": "any"}}], "sla_type": "external", "status": "published"}]}