#!/usr/bin/env python3
"""
Production SLA Consolidation

This script consolidates all custom SLA policies into the existing Default SLA
"""

import json
import requests
import sys
from typing import Dict, List, Any

# Production token
AUTH_TOKEN = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ind5bTMzT0dhMG84TzdNN200OC1pZCJ9.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.PImczzCQVkaIQK_aalC7XXozqwRM26aXHD279mZcl8Pem5X1qwOiFH1NEabwQX4ztaP_KPsJb4tZtrtXswa1RH0ehrCQ_35RnLSl3Ui_eP3cIANWCGZn99N1biSCCSAhV5qmLZd-NP9jnUVCNbcSgrZrr3e1-6EzF-n7XXGQd7InVWqjFVMXj--a_6eFk3Fa0sEerFPy7tbhq5HbTYl8rnyxErY6768MlUcdJHBQrhYQp8iM_4Gek_L7p3fNjrQ5Kx2wcM8f6_Jg7BWKk_JbTb-aZi8TA44375pcVm5WN2IM2sT8xI-xn7_r3yfVDue9Lzb5jr-MfT1rp2vkW9WTDg'

API_BASE_URL = "https://app.devrev.ai/api/gateway/internal"

HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US',
    'authorization': AUTH_TOKEN,
    'content-type': 'application/json',
    'x-devrev-client-platform': 'web-product',
    'x-devrev-client-version': '7a3380a',
    'x-devrev-dev-user-don': 'don:identity:dvrv-in-1:devo/2CUcav2K66:devu/5'
}

def load_production_slas() -> Dict[str, Any]:
    """Load production SLAs"""
    try:
        with open('production_slas.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ production_slas.json not found")
        sys.exit(1)

def load_default_sla() -> Dict[str, Any]:
    """Load the Default SLA from paginated response"""
    try:
        with open('default_sla_paginated.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("❌ default_sla_paginated.json not found")
        sys.exit(1)

def find_default_sla(slas_data: Dict[str, Any]) -> Dict[str, Any]:
    """Find the Default SLA - now we load it directly"""
    return load_default_sla()

def transform_policy_for_consolidation(policy: Dict[str, Any], source_sla_name: str) -> Dict[str, Any]:
    """Transform policy for consolidation"""
    transformed_metrics = []
    
    for metric in policy.get('metrics', []):
        transformed_metric = {
            "metric": metric['metric_definition']['id'],
            "target": metric['target'],
            "performance": metric.get('performance', 0)
        }
        
        if 'warning_target' in metric:
            transformed_metric['warning_target'] = metric['warning_target']
        
        # Only include org_schedule if it's not archived
        if 'org_schedule' in metric:
            org_schedule = metric['org_schedule']
            if org_schedule.get('status') != 'archived':
                transformed_metric['org_schedule_id'] = org_schedule['id']
            
        transformed_metrics.append(transformed_metric)
    
    # Transform selector
    selector = policy['selector'].copy()
    
    # Convert objects to IDs
    if 'group' in selector:
        selector['groups'] = [group['id'] for group in selector['group']]
        del selector['group']
    
    if 'parts' in selector:
        selector['parts'] = [part['id'] for part in selector['parts']]
    
    if 'tags' in selector:
        selector['tags'] = [tag['id'] for tag in selector['tags']]
    
    return {
        "name": f"{source_sla_name} - {policy['name']}",
        "metrics": transformed_metrics,
        "selector": selector
    }

def create_consolidated_policies(slas_data: Dict[str, Any], default_sla: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Create consolidated policies"""
    print("🔄 Creating consolidated policies...")

    slas = slas_data.get('slas', [])
    all_policies = []

    # Keep the original Default SLA policy first (highest priority)
    for policy in default_sla.get('policies', []):
        transformed_policy = transform_policy_for_consolidation(policy, "Default")
        all_policies.append(transformed_policy)

    # Add all other SLA policies (only published ones)
    for sla in slas:
        if sla['name'] != 'Default' and sla['status'] == 'published':  # Skip the default SLA and only include published
            sla_name = sla['name']
            print(f"  Adding policies from: {sla_name}")

            for policy in sla.get('policies', []):
                transformed_policy = transform_policy_for_consolidation(policy, sla_name)
                all_policies.append(transformed_policy)

    print(f"✅ Total consolidated policies: {len(all_policies)}")
    return all_policies

def transition_sla_status(sla_id: str, status: str, sla_name: str = "") -> bool:
    """Transition SLA status"""
    print(f"🔄 Transitioning {sla_name} to {status}")
    
    transition_payload = {
        "id": sla_id,
        "status": status
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/slas.transition",
            headers=HEADERS,
            json=transition_payload,
            timeout=30
        )
        
        if response.status_code == 200:
            print(f"✅ Successfully transitioned {sla_name} to {status}")
            return True
        else:
            print(f"❌ Failed to transition {sla_name}: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error transitioning {sla_name}: {e}")
        return False

def update_default_sla(default_sla: Dict[str, Any], consolidated_policies: List[Dict[str, Any]]) -> bool:
    """Update the Default SLA with all consolidated policies"""
    print("🚀 Updating Default SLA with consolidated policies...")
    
    sla_id = default_sla['id']
    
    # Step 1: Transition to draft
    if not transition_sla_status(sla_id, 'draft', 'Default SLA'):
        return False
    
    # Step 2: Update with consolidated policies
    update_payload = {
        "id": sla_id,
        "name": "Default (All Policies Consolidated)",
        "description": "Default SLA containing all policies from all 50 SLAs in production - for cases where SLA not defined",
        "policies": consolidated_policies
    }
    
    try:
        response = requests.post(
            f"{API_BASE_URL}/slas.update",
            headers=HEADERS,
            json=update_payload,
            timeout=60
        )
        
        if response.status_code == 201:
            print("✅ Successfully updated Default SLA")
            
            # Step 3: Transition back to published
            if transition_sla_status(sla_id, 'published', 'Default SLA'):
                return True
            else:
                print("⚠️  SLA updated but failed to publish. Please publish manually.")
                return False
        else:
            print(f"❌ Failed to update Default SLA: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Error updating Default SLA: {e}")
        return False

def main():
    print("🏭 PRODUCTION SLA CONSOLIDATION")
    print("=" * 60)
    
    # Load production SLAs
    slas_data = load_production_slas()
    slas = slas_data.get('slas', [])
    
    # Find Default SLA
    default_sla = find_default_sla(slas_data)
    
    print(f"📊 Current Status:")
    print(f"  Total SLAs: {len(slas)}")
    print(f"  Default SLA: {default_sla['name']} ({len(default_sla.get('policies', []))} policies) - Status: {default_sla['status']}")

    # Count only published SLAs for consolidation
    published_slas = [sla for sla in slas if sla['status'] == 'published']
    print(f"  Published SLAs to consolidate: {len(published_slas)}")

    total_policies = sum(len(sla.get('policies', [])) for sla in published_slas)
    default_policies = len(default_sla.get('policies', []))
    print(f"  Total Policies to consolidate: {total_policies} + {default_policies} (default) = {total_policies + default_policies}")
    
    print(f"\n⚠️  WARNING: This will consolidate ALL {total_policies} policies into the Default SLA!")
    print("This affects PRODUCTION traffic!")
    
    confirm = input("\nAre you absolutely sure you want to proceed? (yes/no): ").lower().strip()
    if confirm != 'yes':
        print("❌ Consolidation cancelled.")
        return
    
    # Create consolidated policies
    consolidated_policies = create_consolidated_policies(slas_data, default_sla)
    
    # Update Default SLA
    success = update_default_sla(default_sla, consolidated_policies)
    
    if success:
        print(f"\n🎉 CONSOLIDATION COMPLETED!")
        print(f"✅ Default SLA now contains {len(consolidated_policies)} policies")
        print(f"✅ All policies from {len(slas)} SLAs consolidated")
        print(f"\n⚠️  Next steps:")
        print(f"1. Test the consolidated SLA with sample tickets")
        print(f"2. Monitor SLA performance")
        print(f"3. Archive old SLAs when confident")
    else:
        print(f"\n❌ Consolidation failed!")

if __name__ == "__main__":
    main()
