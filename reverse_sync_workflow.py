"""
Reverse Sync Workflow: DevRev to Zendesk Contact Synchronization
"""
import os
import yaml
import logging
from typing import Dict, Any, Optional
from datetime import datetime

from zendesk_client import ZendeskClient, ZendeskUser, DuplicateEmailError, ZendeskAPIError
from sync_mapper_client import SyncMapperClient, SyncMapperR<PERSON>ord, SyncMapperAPIError


class ReverseSyncWorkflow:
    """
    Main workflow for reverse syncing contacts from DevRev to Zendesk
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        self.config = self._load_config(config_path)
        self._setup_logging()
        self._setup_clients()
    
    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from YAML file"""
        with open(config_path, 'r') as f:
            return yaml.safe_load(f)
    
    def _setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=getattr(logging, self.config['logging']['level']),
            format=self.config['logging']['format']
        )
        self.logger = logging.getLogger(__name__)
    
    def _setup_clients(self):
        """Setup API clients"""
        # Zendesk client
        zendesk_config = self.config['zendesk']
        zendesk_email = os.getenv('ZENDESK_EMAIL')
        zendesk_token = os.getenv('ZENDESK_API_TOKEN')
        
        if not zendesk_email or not zendesk_token:
            raise ValueError("ZENDESK_EMAIL and ZENDESK_API_TOKEN environment variables must be set")
        
        self.zendesk_client = ZendeskClient(
            base_url=zendesk_config['base_url'],
            email=zendesk_email,
            api_token=zendesk_token
        )
        
        # DevRev sync mapper client
        devrev_config = self.config['devrev']
        devrev_token = os.getenv('DEVREV_AUTH_TOKEN')
        
        if not devrev_token:
            raise ValueError("DEVREV_AUTH_TOKEN environment variable must be set")
        
        self.sync_mapper_client = SyncMapperClient(
            base_url=devrev_config['base_url'],
            auth_token=devrev_token
        )
        
        self.sync_config = self.config['sync']
    
    def process_ticket_contact(self, ticket_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a ticket's contact for reverse sync
        
        Args:
            ticket_data: Ticket data from DevRev containing contact information
            
        Returns:
            Result of the sync operation
        """
        self.logger.info(f"Processing ticket contact for ticket: {ticket_data.get('id')}")
        
        # Extract contact information from ticket
        contact_info = self._extract_contact_info(ticket_data)
        if not contact_info:
            return {"status": "skipped", "reason": "No contact information found"}
        
        # Get DevRev user ID (reported_by from ticket)
        devrev_user_id = ticket_data.get('reported_by')
        if not devrev_user_id:
            return {"status": "error", "reason": "No reported_by field found in ticket"}
        
        try:
            # Step 1: Try to create user in Zendesk
            zendesk_user_result = self._create_zendesk_user(contact_info)
            
            # Step 2: Create sync mapper record
            sync_mapper_result = self._create_sync_mapper_record(
                zendesk_user_id=zendesk_user_result['user']['id'],
                devrev_user_id=devrev_user_id
            )
            
            return {
                "status": "success",
                "zendesk_user_id": zendesk_user_result['user']['id'],
                "devrev_user_id": devrev_user_id,
                "sync_mapper_record": sync_mapper_result
            }
            
        except DuplicateEmailError as e:
            # Handle duplicate email case
            return self._handle_duplicate_contact(contact_info, devrev_user_id)
            
        except Exception as e:
            self.logger.error(f"Error processing ticket contact: {e}")
            return {"status": "error", "reason": str(e)}
    
    def _extract_contact_info(self, ticket_data: Dict[str, Any]) -> Optional[Dict[str, str]]:
        """
        Extract contact information from ticket data
        
        Args:
            ticket_data: Ticket data from DevRev
            
        Returns:
            Contact information dict with name and email
        """
        # This is a placeholder - actual implementation depends on DevRev ticket structure
        # You'll need to adjust this based on how contact info is stored in DevRev tickets
        
        # Example extraction logic:
        reported_by = ticket_data.get('reported_by')
        if not reported_by:
            return None
        
        # You might need to make an API call to get user details from DevRev
        # For now, using placeholder logic
        contact_info = {
            "name": f"Unknown {reported_by}",  # Placeholder
            "email": f"{reported_by}@example.com"  # Placeholder
        }
        
        return contact_info
    
    def _create_zendesk_user(self, contact_info: Dict[str, str]) -> Dict[str, Any]:
        """
        Create a new user in Zendesk
        
        Args:
            contact_info: Contact information
            
        Returns:
            Zendesk API response
        """
        user_data = ZendeskUser(
            name=contact_info['name'],
            email=contact_info['email'],
            role=self.sync_config['default_user_role'],
            verified=self.sync_config['auto_verify_users']
        )
        
        return self.zendesk_client.create_user(user_data)
    
    def _create_sync_mapper_record(self, zendesk_user_id: int, devrev_user_id: str) -> Dict[str, Any]:
        """
        Create a sync mapper record linking Zendesk and DevRev users
        
        Args:
            zendesk_user_id: Zendesk user ID
            devrev_user_id: DevRev user ID
            
        Returns:
            Sync mapper API response
        """
        record_data = SyncMapperRecord(
            sync_unit=self.sync_config['sync_unit_id'],
            external_ids=[str(zendesk_user_id)],  # Zendesk user ID as external_id
            targets=[devrev_user_id]  # DevRev user ID as target
        )
        
        return self.sync_mapper_client.create_sync_mapper_record(record_data)
    
    def _handle_duplicate_contact(self, contact_info: Dict[str, str], devrev_user_id: str) -> Dict[str, Any]:
        """
        Handle duplicate contact scenario
        
        Args:
            contact_info: Contact information
            devrev_user_id: DevRev user ID
            
        Returns:
            Result of duplicate handling
        """
        strategy = self.sync_config['duplicate_strategy']
        
        if strategy == "skip":
            self.logger.info(f"Skipping duplicate contact: {contact_info['email']}")
            return {"status": "skipped", "reason": "Duplicate contact, skipping as configured"}
        
        elif strategy == "update":
            try:
                # Find existing user in Zendesk
                existing_user = self.zendesk_client.find_user_by_email(contact_info['email'])
                if not existing_user:
                    return {"status": "error", "reason": "Duplicate error but user not found"}
                
                # Update the user
                update_data = {
                    "name": contact_info['name'],
                    "verified": self.sync_config['auto_verify_users']
                }
                
                updated_user = self.zendesk_client.update_user(
                    existing_user['id'], 
                    update_data
                )
                
                # Check if sync mapper record already exists
                existing_record = self.sync_mapper_client.find_sync_mapper_record(
                    self.sync_config['sync_unit_id'],
                    str(existing_user['id'])
                )
                
                if existing_record:
                    # Update existing record
                    sync_mapper_result = self.sync_mapper_client.update_sync_mapper_record(
                        existing_record['id'],
                        {"updated_at": datetime.utcnow().isoformat()}
                    )
                else:
                    # Create new sync mapper record
                    sync_mapper_result = self._create_sync_mapper_record(
                        existing_user['id'],
                        devrev_user_id
                    )
                
                return {
                    "status": "updated",
                    "zendesk_user_id": existing_user['id'],
                    "devrev_user_id": devrev_user_id,
                    "sync_mapper_record": sync_mapper_result
                }
                
            except Exception as e:
                self.logger.error(f"Error updating duplicate contact: {e}")
                return {"status": "error", "reason": f"Failed to update duplicate contact: {e}"}
        
        else:
            return {"status": "error", "reason": f"Unknown duplicate strategy: {strategy}"}


def main():
    """Example usage of the reverse sync workflow"""
    try:
        workflow = ReverseSyncWorkflow()
        
        # Example ticket data (you'll need to adjust this based on actual DevRev ticket structure)
        example_ticket = {
            "id": "don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1",
            "reported_by": "don:core:dvrv-us-1:devo/1cVRZVoinn:revuser/123",
            "title": "Example ticket",
            "description": "This is an example ticket"
        }
        
        result = workflow.process_ticket_contact(example_ticket)
        print(f"Sync result: {result}")
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main() 