#!/usr/bin/env python3
"""
Search for Default SLA more broadly
"""

import json

def search_default_sla():
    """Search for Default SLA more broadly"""
    try:
        with open('production_slas.json', 'r') as f:
            data = json.load(f)
    except FileNotFoundError:
        print("❌ production_slas.json not found")
        return
    
    slas = data.get('slas', [])
    
    print(f"🔍 SEARCHING FOR DEFAULT SLA")
    print(f"Total SLAs: {len(slas)}")
    print("=" * 50)
    
    # Search by different criteria
    default_candidates = []
    
    for sla in slas:
        name = sla['name']
        sla_id = sla['id']
        policies_count = len(sla.get('policies', []))
        
        # Check if name contains "default" (case insensitive)
        if 'default' in name.lower():
            default_candidates.append(('Name contains "default"', sla))
        
        # Check if it's marked as default SLA type
        if sla.get('sla_type') == 'default':
            default_candidates.append(('SLA type is "default"', sla))
        
        # Check if it has minimal policies (might be catch-all)
        if policies_count <= 2:
            default_candidates.append(('Has minimal policies', sla))
    
    if default_candidates:
        print("🎯 POTENTIAL DEFAULT SLA CANDIDATES:")
        for reason, sla in default_candidates:
            print(f"\n{reason}:")
            print(f"  Name: {sla['name']}")
            print(f"  ID: {sla['id']}")
            print(f"  Policies: {len(sla.get('policies', []))}")
            print(f"  SLA Type: {sla.get('sla_type', 'N/A')}")
    else:
        print("❌ No obvious Default SLA candidates found")
    
    # Show all SLA names for manual inspection
    print(f"\n📋 ALL SLA NAMES:")
    for i, sla in enumerate(slas, 1):
        name = sla['name']
        policies = len(sla.get('policies', []))
        sla_type = sla.get('sla_type', 'N/A')
        print(f"{i:2d}. {name} ({policies} policies, type: {sla_type})")
    
    # Look for SLAs with very basic policies
    print(f"\n🔍 SLAs WITH MINIMAL POLICIES (≤2):")
    minimal_slas = [sla for sla in slas if len(sla.get('policies', [])) <= 2]
    
    for sla in minimal_slas:
        print(f"\n📋 {sla['name']} ({len(sla.get('policies', []))} policies)")
        for policy in sla.get('policies', []):
            print(f"  - {policy['name']}")
            # Show metrics briefly
            for metric in policy.get('metrics', []):
                metric_name = metric['metric_definition']['name']
                target = metric['target']
                print(f"    * {metric_name}: {target}min")

if __name__ == "__main__":
    search_default_sla()
