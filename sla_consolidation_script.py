#!/usr/bin/env python3
"""
SLA Consolidation Script

This script consolidates specific SLAs into a default SLA by:
1. Analyzing current SLAs
2. Identifying the "Aug-4.08 p.m" SLA as the default
3. Moving policies from "Global Assure test" and "Hero Moto Fincorp Test" to the default SLA
4. Creating API calls to update the default SLA and archive the specific ones
"""

import json
import requests
import sys
from typing import Dict, List, Any

# Configuration
API_BASE_URL = "https://app.devrev.ai/api/gateway/internal"
HEADERS = {
    'accept': 'application/json, text/plain, */*',
    'accept-language': 'en-US',
    'authorization': 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ind5bTMzT0dhMG84TzdNN200OC1pZCJ9.***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.c14ljGIzqm731Y5uvWdl_1xFnG77XfrnmQo2LkT5UUekPjg4-4WgfeID_bbbSP8YLL3K3RZdOTRygO0bG98WZPDeAvItxe6EsLHWqThQdjI7MgpXRycRwVYXNpcvJRRbvROcYM4bjy1BJD33o-cZmPxXd3JRc2Iwwk-cDAqSz0XkAeJoNaPCVJhc0Y4NMsTo0HGg2bIQkYetJBhNzQB8RzEgtOYXMvr6cKHIw9H1ScdxKX0O17MScXfbH1vlDdc8zVf0wZfeW3k4__pGP_60IAT3QBWieThMcaysLm2V0vljrnlfV2sMa-wPYOsSwuytQT4QETCvicSd9rk04Wa0zQ',
    'content-type': 'application/json',
    'priority': 'u=1, i',
    'referer': 'https://app.devrev.ai/',
    'sec-ch-ua': '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'sec-fetch-dest': 'empty',
    'sec-fetch-mode': 'cors',
    'sec-fetch-site': 'same-origin',
    'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/139.0.0.0 Safari/537.36',
    'x-devrev-client-id': 'ai.devrev.web-product.prod',
    'x-devrev-client-platform': 'web-product',
    'x-devrev-client-version': '7a3380a',
    'x-devrev-dev-user-don': 'don:identity:dvrv-in-1:devo/2113v1Epxh:devu/23'
}

def load_current_slas() -> Dict[str, Any]:
    """Load current SLAs from the JSON file"""
    try:
        with open('current_slas.json', 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print("Error: current_slas.json not found. Please run the curl command first.")
        sys.exit(1)

def analyze_slas(slas_data: Dict[str, Any]) -> None:
    """Analyze current SLA structure"""
    print("=== Current SLA Analysis ===")
    slas = slas_data.get('slas', [])
    
    for sla in slas:
        print(f"\nSLA: {sla['name']} (ID: {sla['id']})")
        print(f"  Display ID: {sla['display_id']}")
        print(f"  Status: {sla['status']}")
        print(f"  Created: {sla['created_date']}")
        print(f"  Policies: {len(sla.get('policies', []))}")
        
        for i, policy in enumerate(sla.get('policies', [])):
            selector = policy.get('selector', {})
            custom_fields = selector.get('custom_fields', {})
            print(f"    Policy {i+1}: {policy['name']}")
            if custom_fields:
                print(f"      Custom fields: {custom_fields}")
            if 'parts' in selector:
                print(f"      Parts: {len(selector['parts'])}")
            if 'tags' in selector:
                print(f"      Tags: {len(selector['tags'])}")

def find_target_slas(slas_data: Dict[str, Any]) -> Dict[str, Any]:
    """Find the target SLAs for consolidation"""
    slas = slas_data.get('slas', [])
    
    target_slas = {
        'default_sla': None,
        'slas_to_consolidate': []
    }
    
    for sla in slas:
        if sla['name'] == 'Aug-4.08 p.m':
            target_slas['default_sla'] = sla
        elif sla['name'] in ['Global Assure test', 'Hero Moto Fincorp Test']:
            target_slas['slas_to_consolidate'].append(sla)
    
    return target_slas

def create_consolidated_policies(target_slas: Dict[str, Any]) -> List[Dict[str, Any]]:
    """Create consolidated policies for the default SLA"""
    default_sla = target_slas['default_sla']
    slas_to_consolidate = target_slas['slas_to_consolidate']
    
    # Start with existing default SLA policies
    consolidated_policies = default_sla.get('policies', []).copy()
    
    # Add policies from SLAs to consolidate
    for sla in slas_to_consolidate:
        for policy in sla.get('policies', []):
            # Create a new policy with modified selector to make it more general
            new_policy = policy.copy()
            
            # Modify the policy name to indicate it came from a specific SLA
            new_policy['name'] = f"{sla['name']} - {policy['name']}"
            
            consolidated_policies.append(new_policy)
    
    return consolidated_policies

def create_update_payload(default_sla: Dict[str, Any], consolidated_policies: List[Dict[str, Any]]) -> Dict[str, Any]:
    """Create the payload for updating the default SLA"""
    return {
        "id": default_sla['id'],
        "name": "Default SLA (Consolidated)",
        "description": "Consolidated SLA containing policies from Global Assure test, Hero Moto Fincorp Test, and other specific SLAs",
        "policies": consolidated_policies
    }

def main():
    print("SLA Consolidation Script")
    print("=" * 50)
    
    # Load current SLAs
    slas_data = load_current_slas()
    
    # Analyze current structure
    analyze_slas(slas_data)
    
    # Find target SLAs
    target_slas = find_target_slas(slas_data)
    
    if not target_slas['default_sla']:
        print("\nError: Could not find 'Aug-4.08 p.m' SLA to use as default")
        sys.exit(1)
    
    if not target_slas['slas_to_consolidate']:
        print("\nError: Could not find SLAs to consolidate")
        sys.exit(1)
    
    print(f"\n=== Consolidation Plan ===")
    print(f"Default SLA: {target_slas['default_sla']['name']} ({target_slas['default_sla']['id']})")
    print(f"SLAs to consolidate:")
    for sla in target_slas['slas_to_consolidate']:
        print(f"  - {sla['name']} ({sla['id']})")
    
    # Create consolidated policies
    consolidated_policies = create_consolidated_policies(target_slas)
    print(f"\nTotal policies after consolidation: {len(consolidated_policies)}")
    
    # Create update payload
    update_payload = create_update_payload(target_slas['default_sla'], consolidated_policies)
    
    # Save the update payload for review
    with open('sla_update_payload.json', 'w') as f:
        json.dump(update_payload, f, indent=2)
    
    print(f"\nUpdate payload saved to 'sla_update_payload.json'")
    print("\nNext steps:")
    print("1. Review the update payload")
    print("2. Run the update API call")
    print("3. Archive the consolidated SLAs")
    
    # Generate the curl commands
    print(f"\n=== API Commands ===")
    print("1. Update default SLA:")
    print(f"curl -X PUT '{API_BASE_URL}/slas.update' \\")
    print("  -H 'Content-Type: application/json' \\")
    print("  -H 'authorization: [YOUR_TOKEN]' \\")
    print("  -d @sla_update_payload.json")
    
    print("\n2. Archive consolidated SLAs:")
    for sla in target_slas['slas_to_consolidate']:
        print(f"# Archive {sla['name']}")
        print(f"curl -X PUT '{API_BASE_URL}/slas.update' \\")
        print("  -H 'Content-Type: application/json' \\")
        print("  -H 'authorization: [YOUR_TOKEN]' \\")
        print(f"  -d '{{\"id\": \"{sla['id']}\", \"status\": \"archived\"}}'")

if __name__ == "__main__":
    main()
