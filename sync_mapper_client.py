"""
DevRev Sync Mapper API Client
"""
import requests
import logging
from typing import Dict, List, Any
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class SyncMapperRecord(BaseModel):
    """Sync mapper record model"""
    sync_unit: str
    external_ids: List[str]
    targets: List[str]
    status: str = "operational"  # Default status as mentioned by Rok


class SyncMapperClient:
    """Client for interacting with DevRev Sync Mapper API"""
    
    def __init__(self, base_url: str, auth_token: str):
        self.base_url = base_url.rstrip('/')
        self.auth_token = auth_token
        self.session = requests.Session()
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Authorization': auth_token
        })
    
    def create_sync_mapper_record(self, record_data: SyncMapperRecord) -> Dict[str, Any]:
        """
        Create a new sync mapper record
        
        Args:
            record_data: Sync mapper record data
            
        Returns:
            Response data from DevRev API
        """
        url = f"{self.base_url}/internal/airdrop.sync-mapper-record.create"
        
        try:
            logger.info(f"Creating sync mapper record for external_ids: {record_data.external_ids}")
            response = self.session.post(url, json=record_data.model_dump())
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Successfully created sync mapper record")
            return result
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"Failed to create sync mapper record: {e}")
            if response.status_code == 400:
                error_data = response.json()
                logger.error(f"Bad request error: {error_data}")
            raise SyncMapperAPIError(f"Failed to create sync mapper record: {e}")
    
    def update_sync_mapper_record(self, record_id: str, record_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing sync mapper record
        
        Args:
            record_id: ID of the sync mapper record to update
            record_data: Updated record data
            
        Returns:
            Response data from DevRev API
        """
        url = f"{self.base_url}/internal/airdrop.sync-mapper-record.update"
        payload = {"id": record_id, **record_data}
        
        try:
            logger.info(f"Updating sync mapper record: {record_id}")
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Successfully updated sync mapper record: {record_id}")
            return result
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"Failed to update sync mapper record {record_id}: {e}")
            raise SyncMapperAPIError(f"Failed to update sync mapper record: {e}")
    
    def find_sync_mapper_record(self, sync_unit: str, external_id: str) -> Dict[str, Any]:
        """
        Find a sync mapper record by sync unit and external ID
        
        Args:
            sync_unit: Sync unit ID
            external_id: External ID to search for
            
        Returns:
            Sync mapper record data if found
        """
        url = f"{self.base_url}/internal/airdrop.sync-mapper-record.list"
        params = {
            "sync_unit": sync_unit,
            "external_ids": [external_id]
        }
        
        try:
            logger.info(f"Searching for sync mapper record with external_id: {external_id}")
            response = self.session.post(url, json=params)
            response.raise_for_status()
            
            result = response.json()
            records = result.get("sync_mapper_records", [])
            
            if records:
                logger.info(f"Found existing sync mapper record for external_id: {external_id}")
                return records[0]
            
            return None
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"Failed to search for sync mapper record: {e}")
            raise SyncMapperAPIError(f"Failed to search for sync mapper record: {e}")


class SyncMapperAPIError(Exception):
    """Raised when DevRev Sync Mapper API returns an error"""
    pass 