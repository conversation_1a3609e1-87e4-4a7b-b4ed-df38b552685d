#!/bin/bash

# DevRev API Authentication Testing with curl
DEVREV_TOKEN="9GjTLCcXDBZX6i7COunplNdsyfK0o6yn5nJ50iTA"
DEVREV_BASE_URL="https://api.dev.devrev-eng.ai"

echo "Testing DevRev API Authentication Methods"
echo "========================================="

# Test data
TEST_DATA='{
  "sync_unit": "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758",
  "external_ids": ["39224878322580"],
  "targets": ["don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1"]
}'

echo -e "\n1. Testing Bearer Token Authentication..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $DEVREV_TOKEN" \
  -d "$TEST_DATA" \
  "$DEVREV_BASE_URL/internal/airdrop.sync-mapper-record.create" | jq

echo -e "\n" && echo "Press Enter to continue..." && read

echo -e "\n2. Testing Token Only Authentication..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: $DEVREV_TOKEN" \
  -d "$TEST_DATA" \
  "$DEVREV_BASE_URL/internal/airdrop.sync-mapper-record.create" | jq

echo -e "\n" && echo "Press Enter to continue..." && read

echo -e "\n3. Testing X-API-Key Authentication..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-API-Key: $DEVREV_TOKEN" \
  -d "$TEST_DATA" \
  "$DEVREV_BASE_URL/internal/airdrop.sync-mapper-record.create" | jq

echo -e "\n" && echo "Press Enter to continue..." && read

echo -e "\n4. Testing X-Auth-Token Authentication..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "X-Auth-Token: $DEVREV_TOKEN" \
  -d "$TEST_DATA" \
  "$DEVREV_BASE_URL/internal/airdrop.sync-mapper-record.create" | jq

echo -e "\n" && echo "Press Enter to continue..." && read

echo -e "\n5. Testing DevRev-Token Authentication..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "DevRev-Token: $DEVREV_TOKEN" \
  -d "$TEST_DATA" \
  "$DEVREV_BASE_URL/internal/airdrop.sync-mapper-record.create" | jq

echo -e "\n" && echo "Press Enter to continue..." && read

echo -e "\n6. Testing Basic Auth (if token is username:password format)..."
curl -X POST \
  -H "Content-Type: application/json" \
  -u "$DEVREV_TOKEN:" \
  -d "$TEST_DATA" \
  "$DEVREV_BASE_URL/internal/airdrop.sync-mapper-record.create" | jq

echo -e "\nAuthentication testing completed!" 