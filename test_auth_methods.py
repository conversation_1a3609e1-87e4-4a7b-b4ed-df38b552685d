"""
Test different authentication methods with the DevRev token
"""
import requests
import json

# DevRev token provided by <PERSON>V_TOKEN = "9GjTLCcXDBZX6i7COunplNdsyfK0o6yn5nJ50iTA"
DEVREV_BASE_URL = "https://api.dev.devrev-eng.ai"

def test_auth_methods():
    """Test different authentication header formats"""
    
    test_data = {
        "sync_unit": "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758",
        "external_ids": ["39224878322580"],
        "targets": ["don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1"]
    }
    
    url = f"{DEVREV_BASE_URL}/internal/airdrop.sync-mapper-record.create"
    
    # Test different authentication methods
    auth_methods = [
        {
            "name": "Bearer Token",
            "headers": {
                'Content-Type': 'application/json',
                'Authorization': f'Bearer {DEVREV_TOKEN}'
            }
        },
        {
            "name": "Token Only",
            "headers": {
                'Content-Type': 'application/json',
                'Authorization': DEVREV_TOKEN
            }
        },
        {
            "name": "X-API-Key",
            "headers": {
                'Content-Type': 'application/json',
                'X-API-Key': DEVREV_TOKEN
            }
        },
        {
            "name": "X-Auth-Token",
            "headers": {
                'Content-Type': 'application/json',
                'X-Auth-Token': DEVREV_TOKEN
            }
        },
        {
            "name": "DevRev-Token",
            "headers": {
                'Content-Type': 'application/json',
                'DevRev-Token': DEVREV_TOKEN
            }
        }
    ]
    
    for method in auth_methods:
        print(f"\nTesting: {method['name']}")
        print("-" * 40)
        print(f"Headers: {json.dumps(method['headers'], indent=2)}")
        
        try:
            response = requests.post(url, headers=method['headers'], json=test_data)
            print(f"Status Code: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ SUCCESS! Response: {json.dumps(result, indent=2)}")
                return method['name']  # Return the successful method
            else:
                print(f"❌ Error: {response.text}")
                
        except Exception as e:
            print(f"❌ Exception: {e}")
    
    return None

def test_health_check():
    """Test if the API is accessible at all"""
    
    print("\nTesting API Health Check...")
    print("-" * 40)
    
    # Try a simple GET request to see if the API is accessible
    try:
        response = requests.get(f"{DEVREV_BASE_URL}/health", timeout=10)
        print(f"Health check status: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Health check failed: {e}")
    
    # Try the base URL
    try:
        response = requests.get(DEVREV_BASE_URL, timeout=10)
        print(f"Base URL status: {response.status_code}")
        print(f"Response: {response.text[:200]}...")
    except Exception as e:
        print(f"Base URL check failed: {e}")

def test_with_different_endpoints():
    """Test different API endpoints to see if any work"""
    
    print("\nTesting Different Endpoints...")
    print("-" * 40)
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {DEVREV_TOKEN}'
    }
    
    endpoints = [
        "/internal/airdrop.sync-mapper-record.list",
        "/internal/airdrop.sync-mapper-record.get",
        "/api/v1/sync-mapper-records",
        "/api/v1/sync-mapper-record"
    ]
    
    for endpoint in endpoints:
        url = f"{DEVREV_BASE_URL}{endpoint}"
        print(f"\nTesting: {endpoint}")
        
        try:
            response = requests.post(url, headers=headers, json={})
            print(f"Status: {response.status_code}")
            if response.status_code != 404:
                print(f"Response: {response.text[:200]}...")
        except Exception as e:
            print(f"Error: {e}")

def main():
    """Run all authentication tests"""
    print("DevRev API Authentication Testing")
    print("=" * 50)
    
    # Test health check first
    test_health_check()
    
    # Test different auth methods
    successful_method = test_auth_methods()
    
    if successful_method:
        print(f"\n🎉 SUCCESS! Working authentication method: {successful_method}")
    else:
        print(f"\n❌ No authentication method worked. Testing different endpoints...")
        test_with_different_endpoints()
        
        print(f"\n💡 Suggestions:")
        print(f"1. Check if the token is still valid")
        print(f"2. Verify the API base URL is correct")
        print(f"3. Check if you need additional headers")
        print(f"4. Contact Ivan to verify the token format")

if __name__ == "__main__":
    main() 