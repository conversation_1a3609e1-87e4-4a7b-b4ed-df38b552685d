#!/bin/bash

# DevRev API Testing with provided token
DEVREV_TOKEN="9GjTLCcXDBZX6i7COunplNdsyfK0o6yn5nJ50iTA"
DEVREV_BASE_URL="https://api.dev.devrev-eng.ai"

echo "Testing DevRev Sync Mapper API with provided token"
echo "=================================================="

# Test 1: Create a sync mapper record
echo -e "\n1. Testing sync mapper record creation..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: $DEVREV_TOKEN" \
  -d '{
    "sync_unit": "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758",
    "external_ids": ["39224878322580"],
    "targets": ["don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1"]
  }' \
  "$DEVREV_BASE_URL/internal/airdrop.sync-mapper-record.create" | jq

echo -e "\n" && echo "Press Enter to continue to next test..." && read

# Test 2: List sync mapper records
echo -e "\n2. Testing sync mapper record listing..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: $DEVREV_TOKEN" \
  -d '{
    "sync_unit": "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758"
  }' \
  "$DEVREV_BASE_URL/internal/airdrop.sync-mapper-record.list" | jq

echo -e "\n" && echo "Press Enter to continue to next test..." && read

# Test 3: Test with different external_id (Zendesk user ID)
echo -e "\n3. Testing with a different Zendesk user ID..."
curl -X POST \
  -H "Content-Type: application/json" \
  -H "Authorization: $DEVREV_TOKEN" \
  -d '{
    "sync_unit": "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758",
    "external_ids": ["test-zendesk-user-123"],
    "targets": ["don:core:dvrv-us-1:devo/1cVRZVoinn:revuser/456"]
  }' \
  "$DEVREV_BASE_URL/internal/airdrop.sync-mapper-record.create" | jq

echo -e "\nTesting completed!" 