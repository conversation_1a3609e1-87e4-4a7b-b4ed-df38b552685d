"""
Debug script to test different API URLs and token formats
"""
import requests
import json

# DevRev token provided by <PERSON>
DEVREV_TOKEN = "9GjTLCcXDBZX6i7COunplNdsyfK0o6yn5nJ50iTA"

def test_different_base_urls():
    """Test different possible base URLs"""
    
    base_urls = [
        "https://api.dev.devrev-eng.ai",
        "https://api.devrev.ai",
        "https://devrev.ai/api",
        "https://api.devrev.com",
        "https://devrev.com/api"
    ]
    
    print("Testing different base URLs...")
    print("=" * 50)
    
    for base_url in base_urls:
        print(f"\nTesting: {base_url}")
        print("-" * 30)
        
        # Test health endpoint
        try:
            response = requests.get(f"{base_url}/health", timeout=5)
            print(f"Health endpoint: {response.status_code}")
            if response.status_code != 404:
                print(f"Response: {response.text[:100]}...")
        except Exception as e:
            print(f"Health endpoint error: {e}")
        
        # Test base URL
        try:
            response = requests.get(base_url, timeout=5)
            print(f"Base URL: {response.status_code}")
            if response.status_code != 404:
                print(f"Response: {response.text[:100]}...")
        except Exception as e:
            print(f"Base URL error: {e}")

def test_token_formats():
    """Test different token formats"""
    
    print("\n\nTesting different token formats...")
    print("=" * 50)
    
    # Test if token needs to be decoded or formatted differently
    token_variations = [
        DEVREV_TOKEN,
        DEVREV_TOKEN.lower(),
        DEVREV_TOKEN.upper(),
        f"Bearer {DEVREV_TOKEN}",
        f"Token {DEVREV_TOKEN}",
        f"Basic {DEVREV_TOKEN}",
        # Try base64 decoding
        DEVREV_TOKEN + "==",  # Add padding
    ]
    
    base_url = "https://api.dev.devrev-eng.ai"
    url = f"{base_url}/internal/airdrop.sync-mapper-record.create"
    
    test_data = {
        "sync_unit": "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758",
        "external_ids": ["test-123"],
        "targets": ["don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1"]
    }
    
    for i, token in enumerate(token_variations):
        print(f"\nToken variation {i+1}: {token[:20]}...")
        print("-" * 40)
        
        headers = {
            'Content-Type': 'application/json',
            'Authorization': token
        }
        
        try:
            response = requests.post(url, headers=headers, json=test_data, timeout=10)
            print(f"Status: {response.status_code}")
            
            if response.status_code == 200:
                print(f"✅ SUCCESS! Token format: {token}")
                return token
            elif response.status_code != 401:
                print(f"Different error: {response.text}")
            else:
                print(f"Still 401: {response.text}")
                
        except Exception as e:
            print(f"Exception: {e}")
    
    return None

def test_without_auth():
    """Test if the API is accessible without authentication"""
    
    print("\n\nTesting without authentication...")
    print("=" * 50)
    
    base_url = "https://api.dev.devrev-eng.ai"
    url = f"{base_url}/internal/airdrop.sync-mapper-record.create"
    
    headers = {
        'Content-Type': 'application/json'
    }
    
    test_data = {
        "sync_unit": "test",
        "external_ids": ["test"],
        "targets": ["test"]
    }
    
    try:
        response = requests.post(url, headers=headers, json=test_data, timeout=10)
        print(f"Status without auth: {response.status_code}")
        print(f"Response: {response.text}")
    except Exception as e:
        print(f"Error: {e}")

def test_curl_equivalent():
    """Show curl commands to test manually"""
    
    print("\n\nCURL Commands to test manually:")
    print("=" * 50)
    
    base_url = "https://api.dev.devrev-eng.ai"
    url = f"{base_url}/internal/airdrop.sync-mapper-record.create"
    
    test_data = {
        "sync_unit": "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758",
        "external_ids": ["test-123"],
        "targets": ["don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1"]
    }
    
    print("1. Test with Bearer token:")
    print(f"""curl -X POST \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer {DEVREV_TOKEN}" \\
  -d '{json.dumps(test_data)}' \\
  "{url}" | jq""")
    
    print("\n2. Test with token only:")
    print(f"""curl -X POST \\
  -H "Content-Type: application/json" \\
  -H "Authorization: {DEVREV_TOKEN}" \\
  -d '{json.dumps(test_data)}' \\
  "{url}" | jq""")
    
    print("\n3. Test with X-API-Key:")
    print(f"""curl -X POST \\
  -H "Content-Type: application/json" \\
  -H "X-API-Key: {DEVREV_TOKEN}" \\
  -d '{json.dumps(test_data)}' \\
  "{url}" | jq""")

def main():
    """Run all debug tests"""
    print("DevRev Token Debug Testing")
    print("=" * 50)
    
    # Test different base URLs
    test_different_base_urls()
    
    # Test different token formats
    working_token = test_token_formats()
    
    # Test without authentication
    test_without_auth()
    
    # Show curl commands
    test_curl_equivalent()
    
    if working_token:
        print(f"\n🎉 Found working token format: {working_token}")
    else:
        print(f"\n❌ No working token format found.")
        print(f"💡 The token might be:")
        print(f"   - Expired")
        print(f"   - For a different environment")
        print(f"   - Requiring additional headers")
        print(f"   - In a different format")

if __name__ == "__main__":
    main() 