"""
Test script using the provided DevRev token
"""
import requests
import json
import os

# DevRev token provided by <PERSON>
DEVREV_TOKEN = "9GjTLCcXDBZX6i7COunplNdsyfK0o6yn5nJ50iTA"
DEVREV_BASE_URL = "https://api.dev.devrev-eng.ai"

def test_sync_mapper_api():
    """Test the sync mapper API with the provided token"""
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': DEVREV_TOKEN
    }
    
    # Test data based on the example from the conversation
    test_data = {
        "sync_unit": "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758",
        "external_ids": ["39224878322580"],
        "targets": ["don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1"]
    }
    
    url = f"{DEVREV_BASE_URL}/internal/airdrop.sync-mapper-record.create"
    
    print("Testing DevRev Sync Mapper API...")
    print(f"URL: {url}")
    print(f"Headers: {json.dumps(headers, indent=2)}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    print("-" * 50)
    
    try:
        response = requests.post(url, headers=headers, json=test_data)
        
        print(f"Status Code: {response.status_code}")
        print(f"Response Headers: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success! Response: {json.dumps(result, indent=2)}")
        else:
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"Exception occurred: {e}")

def test_sync_mapper_list():
    """Test listing sync mapper records"""
    
    headers = {
        'Content-Type': 'application/json',
        'Authorization': DEVREV_TOKEN
    }
    
    # Test listing sync mapper records
    test_data = {
        "sync_unit": "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758"
    }
    
    url = f"{DEVREV_BASE_URL}/internal/airdrop.sync-mapper-record.list"
    
    print("\nTesting Sync Mapper List API...")
    print(f"URL: {url}")
    print(f"Data: {json.dumps(test_data, indent=2)}")
    print("-" * 50)
    
    try:
        response = requests.post(url, headers=headers, json=test_data)
        
        print(f"Status Code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"Success! Found {len(result.get('sync_mapper_records', []))} records")
            print(f"Response: {json.dumps(result, indent=2)}")
        else:
            print(f"Error Response: {response.text}")
            
    except Exception as e:
        print(f"Exception occurred: {e}")

def test_curl_equivalent():
    """Show the equivalent curl command"""
    
    test_data = {
        "sync_unit": "don:integration:dvrv-us-1:devo/1cVRZVoinn:external_system_type/JIRA:external_system/devrev.atlassian.net:sync_unit/************************60f19d6c6758",
        "external_ids": ["39224878322580"],
        "targets": ["don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1"]
    }
    
    print("\nEquivalent CURL command:")
    print("-" * 50)
    print(f"""curl -X POST \\
  -H "Content-Type: application/json" \\
  -H "Authorization: {DEVREV_TOKEN}" \\
  -d '{json.dumps(test_data)}' \\
  '{DEVREV_BASE_URL}/internal/airdrop.sync-mapper-record.create' | jq""")

def main():
    """Run all tests"""
    print("DevRev Sync Mapper API Testing")
    print("=" * 50)
    
    # Test creating a sync mapper record
    test_sync_mapper_api()
    
    # Test listing sync mapper records
    test_sync_mapper_list()
    
    # Show curl equivalent
    test_curl_equivalent()

if __name__ == "__main__":
    main() 