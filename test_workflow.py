"""
Test script for the Reverse Sync Workflow
"""
import unittest
from unittest.mock import Mock, patch, Magic<PERSON>ock
import json

from reverse_sync_workflow import ReverseSyncWorkflow
from zendesk_client import ZendeskUser, DuplicateEmailError
from sync_mapper_client import SyncMapperRecord


class TestReverseSyncWorkflow(unittest.TestCase):
    """Test cases for the Reverse Sync Workflow"""
    
    def setUp(self):
        """Set up test fixtures"""
        # Mock environment variables
        self.env_patcher = patch.dict('os.environ', {
            'ZENDESK_EMAIL': '<EMAIL>',
            'ZENDESK_API_TOKEN': 'test-token',
            'DEVREV_AUTH_TOKEN': 'test-devrev-token'
        })
        self.env_patcher.start()
        
        # Mock config loading
        self.config_patcher = patch('reverse_sync_workflow.ReverseSyncWorkflow._load_config')
        self.mock_config = self.config_patcher.start()
        self.mock_config.return_value = {
            'zendesk': {
                'base_url': 'https://devrev.zendesk.com',
                'api_version': 'v2',
                'auth_type': 'Basic'
            },
            'devrev': {
                'base_url': 'https://api.dev.devrev-eng.ai'
            },
            'sync': {
                'sync_unit_id': 'test-sync-unit-id',
                'duplicate_strategy': 'update',
                'default_user_role': 'end-user',
                'auto_verify_users': True
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            }
        }
        
        # Create workflow instance
        self.workflow = ReverseSyncWorkflow()
        
        # Mock the clients
        self.workflow.zendesk_client = Mock()
        self.workflow.sync_mapper_client = Mock()
    
    def tearDown(self):
        """Clean up after tests"""
        self.env_patcher.stop()
        self.config_patcher.stop()
    
    def test_successful_contact_sync(self):
        """Test successful contact synchronization"""
        # Mock ticket data
        ticket_data = {
            "id": "don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1",
            "reported_by": "don:core:dvrv-us-1:devo/1cVRZVoinn:revuser/123"
        }
        
        # Mock contact info extraction
        with patch.object(self.workflow, '_extract_contact_info') as mock_extract:
            mock_extract.return_value = {
                "name": "John Doe",
                "email": "<EMAIL>"
            }
            
            # Mock Zendesk user creation
            mock_zendesk_response = {
                "user": {
                    "id": 39224878322580,
                    "name": "John Doe",
                    "email": "<EMAIL>"
                }
            }
            self.workflow.zendesk_client.create_user.return_value = mock_zendesk_response
            
            # Mock sync mapper creation
            mock_sync_response = {
                "sync_mapper_record": {
                    "id": "test-record-id",
                    "external_ids": ["39224878322580"],
                    "targets": ["don:core:dvrv-us-1:devo/1cVRZVoinn:revuser/123"]
                }
            }
            self.workflow.sync_mapper_client.create_sync_mapper_record.return_value = mock_sync_response
            
            # Execute workflow
            result = self.workflow.process_ticket_contact(ticket_data)
            
            # Assertions
            self.assertEqual(result['status'], 'success')
            self.assertEqual(result['zendesk_user_id'], 39224878322580)
            self.assertEqual(result['devrev_user_id'], 'don:core:dvrv-us-1:devo/1cVRZVoinn:revuser/123')
            
            # Verify Zendesk user creation was called
            self.workflow.zendesk_client.create_user.assert_called_once()
            call_args = self.workflow.zendesk_client.create_user.call_args[0][0]
            self.assertIsInstance(call_args, ZendeskUser)
            self.assertEqual(call_args.name, "John Doe")
            self.assertEqual(call_args.email, "<EMAIL>")
            
            # Verify sync mapper creation was called
            self.workflow.sync_mapper_client.create_sync_mapper_record.assert_called_once()
            call_args = self.workflow.sync_mapper_client.create_sync_mapper_record.call_args[0][0]
            self.assertIsInstance(call_args, SyncMapperRecord)
            self.assertEqual(call_args.external_ids, ["39224878322580"])
            self.assertEqual(call_args.targets, ["don:core:dvrv-us-1:devo/1cVRZVoinn:revuser/123"])
    
    def test_duplicate_contact_handling(self):
        """Test handling of duplicate contacts"""
        # Mock ticket data
        ticket_data = {
            "id": "don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1",
            "reported_by": "don:core:dvrv-us-1:devo/1cVRZVoinn:revuser/123"
        }
        
        # Mock contact info extraction
        with patch.object(self.workflow, '_extract_contact_info') as mock_extract:
            mock_extract.return_value = {
                "name": "John Doe",
                "email": "<EMAIL>"
            }
            
            # Mock Zendesk user creation to raise DuplicateEmailError
            self.workflow.zendesk_client.create_user.side_effect = DuplicateEmailError("User already exists")
            
            # Mock finding existing user
            mock_existing_user = {
                "id": 39224878322580,
                "name": "John Doe",
                "email": "<EMAIL>"
            }
            self.workflow.zendesk_client.find_user_by_email.return_value = mock_existing_user
            
            # Mock user update
            mock_updated_user = {
                "user": {
                    "id": 39224878322580,
                    "name": "John Doe Updated",
                    "email": "<EMAIL>"
                }
            }
            self.workflow.zendesk_client.update_user.return_value = mock_updated_user
            
            # Mock sync mapper record not found
            self.workflow.sync_mapper_client.find_sync_mapper_record.return_value = None
            
            # Mock sync mapper creation
            mock_sync_response = {
                "sync_mapper_record": {
                    "id": "test-record-id"
                }
            }
            self.workflow.sync_mapper_client.create_sync_mapper_record.return_value = mock_sync_response
            
            # Execute workflow
            result = self.workflow.process_ticket_contact(ticket_data)
            
            # Assertions
            self.assertEqual(result['status'], 'updated')
            self.assertEqual(result['zendesk_user_id'], 39224878322580)
            
            # Verify user was updated
            self.workflow.zendesk_client.update_user.assert_called_once()
            
            # Verify sync mapper record was created
            self.workflow.sync_mapper_client.create_sync_mapper_record.assert_called_once()
    
    def test_missing_contact_info(self):
        """Test handling when no contact information is found"""
        # Mock ticket data
        ticket_data = {
            "id": "don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1",
            "reported_by": "don:core:dvrv-us-1:devo/1cVRZVoinn:revuser/123"
        }
        
        # Mock contact info extraction to return None
        with patch.object(self.workflow, '_extract_contact_info') as mock_extract:
            mock_extract.return_value = None
            
            # Execute workflow
            result = self.workflow.process_ticket_contact(ticket_data)
            
            # Assertions
            self.assertEqual(result['status'], 'skipped')
            self.assertEqual(result['reason'], 'No contact information found')
    
    def test_missing_reported_by(self):
        """Test handling when reported_by field is missing"""
        # Mock ticket data without reported_by
        ticket_data = {
            "id": "don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1",
            "title": "Test ticket"
        }
        
        # Execute workflow
        result = self.workflow.process_ticket_contact(ticket_data)
        
        # Assertions
        self.assertEqual(result['status'], 'error')
        self.assertEqual(result['reason'], 'No reported_by field found in ticket')


def run_manual_test():
    """Run a manual test with actual API calls (requires credentials)"""
    print("Running manual test...")
    
    # This would require actual credentials to be set
    try:
        workflow = ReverseSyncWorkflow()
        
        # Test ticket data
        test_ticket = {
            "id": "don:core:dvrv-us-1:devo/1cVRZVoinn:ticket/1",
            "reported_by": "don:core:dvrv-us-1:devo/1cVRZVoinn:revuser/123"
        }
        
        result = workflow.process_ticket_contact(test_ticket)
        print(f"Manual test result: {json.dumps(result, indent=2)}")
        
    except Exception as e:
        print(f"Manual test failed: {e}")


if __name__ == '__main__':
    # Run unit tests
    unittest.main(verbosity=2, exit=False)
    
    # Uncomment to run manual test (requires credentials)
    # run_manual_test() 