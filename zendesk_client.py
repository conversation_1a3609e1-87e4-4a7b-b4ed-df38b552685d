"""
Zendesk API Client for user operations
"""
import requests
import logging
from typing import Dict, Optional, Any
from pydantic import BaseModel

logger = logging.getLogger(__name__)


class ZendeskUser(BaseModel):
    """Zendesk user model"""
    name: str
    email: str
    role: str = "end-user"
    verified: bool = True
    external_id: Optional[str] = None


class ZendeskClient:
    """Client for interacting with Zendesk API"""
    
    def __init__(self, base_url: str, email: str, api_token: str):
        self.base_url = base_url.rstrip('/')
        self.email = email
        self.api_token = api_token
        self.session = requests.Session()
        self.session.auth = (f"{email}/token", api_token)
        self.session.headers.update({
            'Content-Type': 'application/json',
            'Accept': 'application/json'
        })
    
    def create_user(self, user_data: ZendeskUser) -> Dict[str, Any]:
        """
        Create a new user in Zendesk
        
        Args:
            user_data: User data to create
            
        Returns:
            Response data from Zendesk API
        """
        url = f"{self.base_url}/api/v2/users.json"
        payload = {"user": user_data.model_dump(exclude_none=True)}
        
        try:
            logger.info(f"Creating Zendesk user: {user_data.email}")
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Successfully created Zendesk user with ID: {result['user']['id']}")
            return result
            
        except requests.exceptions.HTTPError as e:
            if response.status_code == 422:
                # Check if it's a duplicate email error
                error_data = response.json()
                if "email" in error_data.get("details", {}):
                    logger.warning(f"Duplicate email error: {user_data.email}")
                    raise DuplicateEmailError(f"User with email {user_data.email} already exists")
            
            logger.error(f"Failed to create Zendesk user: {e}")
            raise ZendeskAPIError(f"Failed to create user: {e}")
    
    def update_user(self, user_id: int, user_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update an existing user in Zendesk
        
        Args:
            user_id: Zendesk user ID
            user_data: Updated user data
            
        Returns:
            Response data from Zendesk API
        """
        url = f"{self.base_url}/api/v2/users/{user_id}.json"
        payload = {"user": user_data}
        
        try:
            logger.info(f"Updating Zendesk user: {user_id}")
            response = self.session.put(url, json=payload)
            response.raise_for_status()
            
            result = response.json()
            logger.info(f"Successfully updated Zendesk user: {user_id}")
            return result
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"Failed to update Zendesk user {user_id}: {e}")
            raise ZendeskAPIError(f"Failed to update user: {e}")
    
    def find_user_by_email(self, email: str) -> Optional[Dict[str, Any]]:
        """
        Find a user by email address
        
        Args:
            email: Email address to search for
            
        Returns:
            User data if found, None otherwise
        """
        url = f"{self.base_url}/api/v2/users/search.json"
        params = {"query": f"email:{email}"}
        
        try:
            response = self.session.get(url, params=params)
            response.raise_for_status()
            
            result = response.json()
            users = result.get("users", [])
            
            if users:
                logger.info(f"Found existing Zendesk user with email: {email}")
                return users[0]
            
            return None
            
        except requests.exceptions.HTTPError as e:
            logger.error(f"Failed to search for user by email {email}: {e}")
            raise ZendeskAPIError(f"Failed to search for user: {e}")


class DuplicateEmailError(Exception):
    """Raised when trying to create a user with an existing email"""
    pass


class ZendeskAPIError(Exception):
    """Raised when Zendesk API returns an error"""
    pass 